// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXFileReference section */
		87748CB82AE26C01002E03D0 /* build-framework.sh */ = {isa = PBXFileReference; lastKnownFileType = text.script.sh; path = "build-framework.sh"; sourceTree = "<group>"; };
		87748CB92AE26C01002E03D0 /* xmplib-framework.podspec */ = {isa = PBXFileReference; lastKnownFileType = text; path = "xmplib-framework.podspec"; sourceTree = "<group>"; };
		87748CBA2AE26C01002E03D0 /* repoPush-framework.sh */ = {isa = PBXFileReference; lastKnownFileType = text.script.sh; path = "repoPush-framework.sh"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXGroup section */
		87748CB02AE26BE0002E03D0 = {
			isa = PBXGroup;
			children = (
				87748CB72AE26C01002E03D0 /* doc */,
			);
			sourceTree = "<group>";
		};
		87748CB72AE26C01002E03D0 /* doc */ = {
			isa = PBXGroup;
			children = (
				87748CB82AE26C01002E03D0 /* build-framework.sh */,
				87748CB92AE26C01002E03D0 /* xmplib-framework.podspec */,
				87748CBA2AE26C01002E03D0 /* repoPush-framework.sh */,
			);
			path = doc;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXProject section */
		87748CB12AE26BE0002E03D0 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1420;
			};
			buildConfigurationList = 87748CB42AE26BE0002E03D0 /* Build configuration list for PBXProject "other" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 87748CB02AE26BE0002E03D0;
			projectDirPath = "";
			projectRoot = "";
			targets = (
			);
		};
/* End PBXProject section */

/* Begin XCBuildConfiguration section */
		87748CB52AE26BE0002E03D0 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
			};
			name = Debug;
		};
		87748CB62AE26BE0002E03D0 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		87748CB42AE26BE0002E03D0 /* Build configuration list for PBXProject "other" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				87748CB52AE26BE0002E03D0 /* Debug */,
				87748CB62AE26BE0002E03D0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 87748CB12AE26BE0002E03D0 /* Project object */;
}
