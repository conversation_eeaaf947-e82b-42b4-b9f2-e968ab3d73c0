##### 使用cocoapods直接托管 swift5编译的 二进制frameworks，仅支持iOS12.2
Pod::Spec.new do |s|
    s.name                    = 'xmplib'
    s.version                 = '1.0.5'
    s.summary                 = 'MyFramework summary.'
    s.homepage                = 'https://www.example.com/'

    s.author                  = { 'MyFramework' => '<EMAIL>' }
    s.license                 = { :type => 'Apache-2.0', :file => 'LICENSE' }

    s.platform                = :ios
    s.source                  = { :http => 'http://fat-app.koofenqi.com/cocoapods/xmp/1.0.4.zip' }

    s.ios.deployment_target   = '9.0'
    # s.frameworks = 'UIKit', 'WebKit', 'Foundation'
    s.ios.vendored_frameworks = 'xmplib.framework'
    s.pod_target_xcconfig = {
        'EXCLUDED_ARCHS[sdk=iphonesimulator*]' => 'arm64'
    }
    s.user_target_xcconfig = { 'EXCLUDED_ARCHS[sdk=iphonesimulator*]' => 'arm64' }
end 