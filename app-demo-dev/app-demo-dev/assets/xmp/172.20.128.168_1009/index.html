<!DOCTYPE html><html lang="zh-CN"><head><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><title></title><script>var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'))
            document.write('<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' + (coverSupport ? ', viewport-fit=cover' : '') + '" />')
            // 获取ua中的statusBarVW
            try{
                var statusBarHeight = navigator.userAgent.match(/statusBarHeight:.*?[;|)]/g)[0];
                statusBarHeight = statusBarHeight.substring(16,statusBarHeight.length-1);
                var width = navigator.userAgent.match(/width:.*?[;|)]/g)[0];
                width = width.substring(6,width.length-1);
                var vw = parseFloat(statusBarHeight) / parseFloat(width) * 100;
                document.documentElement.style.setProperty('--statusBarHeight',vw + "vw")
                window.xmpStatusBarHeight = vw+"vw"
            }catch(e){
                document.documentElement.style.setProperty('--statusBarHeight', "36px")
                window.xmpStatusBarHeight = "36px"
            }</script><link rel="stylesheet" href="./static/index.2772579d.css"><script>// 白屏结束时间
            window.xmpFirstPaint = Date.now()</script></head><body><noscript><strong>Please enable JavaScript to continue.</strong></noscript><div id="app"></div><script>window.xmpFirstScreen = Date.now();</script><script src="./static/js/chunk-vendors.56100db5.js"></script><script src="./static/js/index.a3df25e9.js"></script></body></html>