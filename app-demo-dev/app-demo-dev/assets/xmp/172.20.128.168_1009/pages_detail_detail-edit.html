<!DOCTYPE html><html lang="zh-CN" style="--statusBarHeight:36px; --status-bar-height:0px; --top-window-height:0px; --window-left:0px; --window-right:0px; --window-margin:0px; --window-top:calc(var(--top-window-height) + calc(44px + env(safe-area-inset-top))); --window-bottom:0px;"><head><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><title>详情编辑页</title><script>var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'))
            document.write('<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' + (coverSupport ? ', viewport-fit=cover' : '') + '" />')
            // 获取ua中的statusBarVW
            try{
                var statusBarHeight = navigator.userAgent.match(/statusBarHeight:.*?[;|)]/g)[0];
                statusBarHeight = statusBarHeight.substring(16,statusBarHeight.length-1);
                var width = navigator.userAgent.match(/width:.*?[;|)]/g)[0];
                width = width.substring(6,width.length-1);
                var vw = parseFloat(statusBarHeight) / parseFloat(width) * 100;
                document.documentElement.style.setProperty('--statusBarHeight',vw + "vw")
                window.xmpStatusBarHeight = vw+"vw"
            }catch(e){
                document.documentElement.style.setProperty('--statusBarHeight', "36px")
                window.xmpStatusBarHeight = "36px"
            }</script><meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover"><link rel="stylesheet" href="./static/index.2772579d.css"><script>// 白屏结束时间
            window.xmpFirstPaint = Date.now()</script><style type="text/css">















/*每个页面公共css */</style><style type="text/css">.uni-app--showtabbar uni-page-wrapper {
                    display: block;
                    height: calc(100% - 50px);
                    height: calc(100% - 50px - constant(safe-area-inset-bottom));
                    height: calc(100% - 50px - env(safe-area-inset-bottom));
                  }
.uni-app--showtabbar uni-page-wrapper::after {
                  content: "";
                  display: block;
                  width: 100%;
                  height: 50px;
                  height: calc(50px + constant(safe-area-inset-bottom));
                  height: calc(50px + env(safe-area-inset-bottom));
                }
.uni-app--showtabbar uni-page-head[uni-page-head-type="default"] ~ uni-page-wrapper {
                  height: calc(100% - 44px - 50px);
                  height: calc(100% - 44px - constant(safe-area-inset-top) - 50px - constant(safe-area-inset-bottom));
                  height: calc(100% - 44px - env(safe-area-inset-top) - 50px - env(safe-area-inset-bottom));
                }</style><style type="text/css">.content[data-v-1d413678]{display:flex;flex-direction:column;align-items:center;justify-content:center}.logo[data-v-1d413678]{height:100px;width:var(--statusBarHeight);margin:100px auto 25px auto}.text-area[data-v-1d413678]{display:flex;justify-content:center}.title[data-v-1d413678]{font-size:18px;color:#8f8f94}</style></head><body class="uni-body pages-detail-detail-edit"><noscript><strong>Please enable JavaScript to continue.</strong></noscript><uni-app id="app" class="uni-app--maxwidth"><uni-page data-page="pages/detail/detail-edit"><uni-page-head uni-page-head-type="default"><div class="uni-page-head" style="background-color: rgb(248, 248, 248); color: rgb(0, 0, 0);"><div class="uni-page-head-hd"><div class="uni-page-head-btn"><i class="uni-btn-icon" style="color: rgba(0, 0, 0, 0); font-size: 27px;"></i></div><div class="uni-page-head-ft"></div></div><div class="uni-page-head-bd"><div class="uni-page-head__title" style="font-size: 16px; opacity: 1;"><!----> 详情编辑页 </div></div><!----><div class="uni-page-head-ft"></div></div><div class="uni-placeholder"></div></uni-page-head><!----><uni-page-wrapper><uni-page-body><uni-view data-v-1d413678="" class="content"><uni-image data-v-1d413678="" class="logo"><div style="background-image: url(&quot;./static/logo.png&quot;); background-position: 0% 0%; background-size: 100% 100%; background-repeat: no-repeat;"></div><!----><img src="./static/logo.png" draggable="false"></uni-image><uni-view data-v-1d413678=""><uni-text data-v-1d413678="" class="title"><span>详情编辑页5</span></uni-text></uni-view><uni-view data-v-1d413678="" class="uni-form-item uni-column" style="margin-top: 50vh;"><uni-view data-v-1d413678="" class="title">控制占位符颜色的input</uni-view><uni-input data-v-1d413678="" class="uni-input"><div class="uni-input-wrapper"><div class="uni-input-placeholder input-placeholder" data-v-1d413678="" style="color: rgb(247, 98, 96);">占位符字体是红色的</div><input maxlength="140" step="" enterkeyhint="done" autocomplete="off" type="" class="uni-input-input"><!----></div></uni-input></uni-view></uni-view></uni-page-body></uni-page-wrapper></uni-page><!----><!----><uni-actionsheet><div class="uni-mask uni-actionsheet__mask" style="display: none;"></div><div class="uni-actionsheet"><div class="uni-actionsheet__menu"><!----><!----><div style="max-height: 260px; overflow: hidden;"><div style="transform: translateY(0px) translateZ(0px);"></div></div></div><div class="uni-actionsheet__action"><div class="uni-actionsheet__cell" style="color: rgb(0, 0, 0);"> Cancel </div></div><div></div></div><!----></uni-actionsheet><uni-modal style="display: none;"><div class="uni-mask"></div><div class="uni-modal"><!----><div class="uni-modal__bd"></div><div class="uni-modal__ft"><div class="uni-modal__btn uni-modal__btn_default" style="color: rgb(0, 0, 0);"> Cancel </div><div class="uni-modal__btn uni-modal__btn_primary" style="color: rgb(0, 122, 255);"> OK </div></div></div><!----></uni-modal><!----></uni-app><script>window.xmpFirstScreen = Date.now();</script><script charset="utf-8" src="./static/js/pages-detail-detail-edit.03924718.js"></script><script src="./static/js/chunk-vendors.56100db5.js"></script><script src="./static/js/index.a3df25e9.js"></script><div style="position: absolute; left: 0px; top: 0px; width: 0px; height: 0px; z-index: -1; overflow: hidden; visibility: hidden;"><div style="position: absolute; width: 100px; height: 200px; box-sizing: border-box; overflow: hidden; padding-bottom: env(safe-area-inset-top);"><div style="transition: all 0s ease 0s; animation: 0s ease 0s 1 normal none running none; width: 400px; height: 400px;"></div></div><div style="position: absolute; width: 100px; height: 200px; box-sizing: border-box; overflow: hidden; padding-bottom: env(safe-area-inset-top);"><div style="transition: all 0s ease 0s; animation: 0s ease 0s 1 normal none running none; width: 250%; height: 250%;"></div></div><div style="position: absolute; width: 100px; height: 200px; box-sizing: border-box; overflow: hidden; padding-bottom: env(safe-area-inset-left);"><div style="transition: all 0s ease 0s; animation: 0s ease 0s 1 normal none running none; width: 400px; height: 400px;"></div></div><div style="position: absolute; width: 100px; height: 200px; box-sizing: border-box; overflow: hidden; padding-bottom: env(safe-area-inset-left);"><div style="transition: all 0s ease 0s; animation: 0s ease 0s 1 normal none running none; width: 250%; height: 250%;"></div></div><div style="position: absolute; width: 100px; height: 200px; box-sizing: border-box; overflow: hidden; padding-bottom: env(safe-area-inset-right);"><div style="transition: all 0s ease 0s; animation: 0s ease 0s 1 normal none running none; width: 400px; height: 400px;"></div></div><div style="position: absolute; width: 100px; height: 200px; box-sizing: border-box; overflow: hidden; padding-bottom: env(safe-area-inset-right);"><div style="transition: all 0s ease 0s; animation: 0s ease 0s 1 normal none running none; width: 250%; height: 250%;"></div></div><div style="position: absolute; width: 100px; height: 200px; box-sizing: border-box; overflow: hidden; padding-bottom: env(safe-area-inset-bottom);"><div style="transition: all 0s ease 0s; animation: 0s ease 0s 1 normal none running none; width: 400px; height: 400px;"></div></div><div style="position: absolute; width: 100px; height: 200px; box-sizing: border-box; overflow: hidden; padding-bottom: env(safe-area-inset-bottom);"><div style="transition: all 0s ease 0s; animation: 0s ease 0s 1 normal none running none; width: 250%; height: 250%;"></div></div></div></body></html>