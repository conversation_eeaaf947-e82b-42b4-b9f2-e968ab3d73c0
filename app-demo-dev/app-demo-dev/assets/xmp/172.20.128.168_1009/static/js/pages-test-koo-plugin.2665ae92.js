(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-test-koo-plugin"],{"2cc2":function(t,n,a){"use strict";var e;a.d(n,"b",(function(){return i})),a.d(n,"c",(function(){return o})),a.d(n,"a",(function(){return e}));var i=function(){var t=this,n=t.$createElement,a=t._self._c||n;return a("v-uni-view",{staticClass:"content"},[a("v-uni-image",{staticClass:"logo",attrs:{src:"/static/logo.png"},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.webSocketTest.apply(void 0,arguments)}}}),a("v-uni-view",[a("v-uni-text",{staticClass:"title"},[t._v(t._s(t.title))]),a("v-uni-button",{attrs:{type:"default"},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.pickImageQN.apply(void 0,arguments)}}},[t._v("测试pickImageQN")]),a("v-uni-button",{attrs:{type:"default"},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.getUA.apply(void 0,arguments)}}},[t._v("获取UA2")]),a("v-uni-view",{staticStyle:{width:"px",height:"200px","background-color":"aqua"}})],1)],1)},o=[]},3267:function(t,n,a){"use strict";a.r(n);var e=a("2cc2"),i=a("de62");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(n,t,(function(){return i[t]}))}(o);a("e5a8");var c,u=a("f0c5"),r=Object(u["a"])(i["default"],e["b"],e["c"],!1,null,"6aaa5ae8",null,!1,e["a"],c);n["default"]=r.exports},6021:function(t,n,a){var e=a("24fb");n=e(!1),n.push([t.i,".content[data-v-6aaa5ae8]{display:flex;flex-direction:column;align-items:center;justify-content:center}.logo[data-v-6aaa5ae8]{height:100px;width:100px;margin:100px auto 25px auto}.text-area[data-v-6aaa5ae8]{display:flex;justify-content:center}.title[data-v-6aaa5ae8]{font-size:18px;color:#8f8f94}",""]),t.exports=n},c492:function(t,n,a){var e=a("6021");"string"===typeof e&&(e=[[t.i,e,""]]),e.locals&&(t.exports=e.locals);var i=a("4f06").default;i("23bd24bd",e,!0,{sourceMap:!1,shadowMode:!1})},d1da:function(t,n,a){"use strict";a("7a82"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,a("ac1f"),a("466d"),a("acd8"),a("e9c4");a("4de5");var e={data:function(){return{title:"测试KOOApp中的插件3"}},onLoad:function(){},methods:{getUA:function(){var t=navigator.userAgent.match(/timestamp:.*?[;|)]/g)[0];t=t.substring(10,t.length-1);var n=window.xmpFirstPaint-parseFloat(t),a=window.xmpFirstScreen-parseFloat(t);uni.showModal({title:n+"",content:a+"",success:function(t){}})},pickImageQN:function(){window.xmpCall("pickImageQN",null,(function(t){uni.showModal({title:"提示2",content:JSON.stringify(t),success:function(t){}})}))}}};n.default=e},de62:function(t,n,a){"use strict";a.r(n);var e=a("d1da"),i=a.n(e);for(var o in e)["default"].indexOf(o)<0&&function(t){a.d(n,t,(function(){return e[t]}))}(o);n["default"]=i.a},e5a8:function(t,n,a){"use strict";var e=a("c492"),i=a.n(e);i.a}}]);