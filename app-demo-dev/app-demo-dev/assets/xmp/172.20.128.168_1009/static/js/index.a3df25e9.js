(function(n){function e(e){for(var o,r,c=e[0],l=e[1],s=e[2],u=0,p=[];u<c.length;u++)r=c[u],Object.prototype.hasOwnProperty.call(i,r)&&i[r]&&p.push(i[r][0]),i[r]=0;for(o in l)Object.prototype.hasOwnProperty.call(l,o)&&(n[o]=l[o]);d&&d(e);while(p.length)p.shift()();return a.push.apply(a,s||[]),t()}function t(){for(var n,e=0;e<a.length;e++){for(var t=a[e],o=!0,r=1;r<t.length;r++){var l=t[r];0!==i[l]&&(o=!1)}o&&(a.splice(e--,1),n=c(c.s=t[0]))}return n}var o={},i={index:0},a=[];function r(n){return c.p+"static/js/"+({"pages-detail-detail":"pages-detail-detail","pages-detail-detail-edit":"pages-detail-detail-edit","pages-index-index":"pages-index-index","pages-test-koo-plugin":"pages-test-koo-plugin","pages-test-screen-collector":"pages-test-screen-collector"}[n]||n)+"."+{"pages-detail-detail":"95df101e","pages-detail-detail-edit":"03924718","pages-index-index":"63535306","pages-test-koo-plugin":"2665ae92","pages-test-screen-collector":"c3fb2a25"}[n]+".js"}function c(e){if(o[e])return o[e].exports;var t=o[e]={i:e,l:!1,exports:{}};return n[e].call(t.exports,t,t.exports,c),t.l=!0,t.exports}c.e=function(n){var e=[],t=i[n];if(0!==t)if(t)e.push(t[2]);else{var o=new Promise((function(e,o){t=i[n]=[e,o]}));e.push(t[2]=o);var a,l=document.createElement("script");l.charset="utf-8",l.timeout=120,c.nc&&l.setAttribute("nonce",c.nc),l.src=r(n);var s=new Error;a=function(e){l.onerror=l.onload=null,clearTimeout(u);var t=i[n];if(0!==t){if(t){var o=e&&("load"===e.type?"missing":e.type),a=e&&e.target&&e.target.src;s.message="Loading chunk "+n+" failed.\n("+o+": "+a+")",s.name="ChunkLoadError",s.type=o,s.request=a,t[1](s)}i[n]=void 0}};var u=setTimeout((function(){a({type:"timeout",target:l})}),12e4);l.onerror=l.onload=a,document.head.appendChild(l)}return Promise.all(e)},c.m=n,c.c=o,c.d=function(n,e,t){c.o(n,e)||Object.defineProperty(n,e,{enumerable:!0,get:t})},c.r=function(n){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(n,"__esModule",{value:!0})},c.t=function(n,e){if(1&e&&(n=c(n)),8&e)return n;if(4&e&&"object"===typeof n&&n&&n.__esModule)return n;var t=Object.create(null);if(c.r(t),Object.defineProperty(t,"default",{enumerable:!0,value:n}),2&e&&"string"!=typeof n)for(var o in n)c.d(t,o,function(e){return n[e]}.bind(null,o));return t},c.n=function(n){var e=n&&n.__esModule?function(){return n["default"]}:function(){return n};return c.d(e,"a",e),e},c.o=function(n,e){return Object.prototype.hasOwnProperty.call(n,e)},c.p="./",c.oe=function(n){throw console.error(n),n};var l=window["webpackJsonp"]=window["webpackJsonp"]||[],s=l.push.bind(l);l.push=e,l=l.slice();for(var u=0;u<l.length;u++)e(l[u]);var d=s;a.push([0,"chunk-vendors"]),t()})({0:function(n,e,t){n.exports=t("56d7")},"034f":function(n,e,t){"use strict";var o=t("ef9e"),i=t.n(o);i.a},"23be":function(n,e,t){"use strict";t.r(e);var o=t("3b4e"),i=t.n(o);for(var a in o)["default"].indexOf(a)<0&&function(n){t.d(e,n,(function(){return o[n]}))}(a);e["default"]=i.a},"3b4e":function(n,e,t){"use strict";t("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;t("4de5");var o={onLaunch:function(){console.log("App Launch")},onShow:function(){console.log("App Show")},onHide:function(){console.log("App Hide")}};e.default=o},"3dfd":function(n,e,t){"use strict";t.r(e);var o=t("fd5e"),i=t("23be");for(var a in i)["default"].indexOf(a)<0&&function(n){t.d(e,n,(function(){return i[n]}))}(a);t("034f");var r,c=t("f0c5"),l=Object(c["a"])(i["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],r);e["default"]=l.exports},"4de5":function(n,e,t){"use strict";t("7a82");var o=t("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.xmp=void 0;var i=o(t("53ca"));function a(){return window.xmp||window.webkit&&window.webkit.messageHandlers.xmp}function r(n){try{var e=[],t=JSON.stringify(n,(function(n,t){if(null!=t&&"object"==(0,i.default)(t)){if(e.indexOf(t)>=0)return;e.push(t)}return t}));return t}catch(o){return o.toString()}}t("d3b7"),t("25f0"),t("ac1f"),t("e9c4"),t("c975"),t("8a79"),t("fb6a"),t("baa5"),t("5319"),t("3ca3"),t("ddb0"),t("2b3d"),t("9861"),t("466d"),t("acd8"),window.callbackCache={},window.listenerCache={},window.onNativeCall=function(n){var e=JSON.parse(n);if(e.callbackId)window.callbackCache[e.callbackId]&&window.callbackCache[e.callbackId](e);else if(window.listenerCache[e.method])return window.listenerCache[e.method](e)},window.xmpOn=function(n,e){e&&(window.listenerCache[n]=function(n){return e(n.data)})},window.xmpCall=function(n,e,t){var o=Math.random().toString();if(window.koosdk){var i=0,a=2e9;o=Math.floor(Math.random()*(a-i+1))+i}var r={method:n,data:e,callbackId:o};if(window.xmp)window.xmp.exec(JSON.stringify(r));else if(window.webkit&&window.webkit.messageHandlers.xmp)window.webkit.messageHandlers.xmp.postMessage(JSON.stringify(r));else if(window.koosdk)window.koosdk.exec(JSON.stringify(r));else{if(!window.webkit||!window.webkit.messageHandlers.koosdk)return;window.webkit.messageHandlers.koosdk.postMessage(JSON.stringify(r))}t&&(window.callbackCache[o]=function(n){window.callbackCache[o]=void 0,t(n.data)})};var c=console.log;function l(){var n=window.location.pathname;return n.endsWith("/")&&(n=n.substring(0,n.length-1)),n.indexOf(".html")&&(n=n.slice(0,n.lastIndexOf("/"))),window.location.origin+n}function s(n){return-1!==window.location.href.indexOf(".html#/")?l()+n.replace(/\//g,"_").replace(/_/,"/")+".html#"+n:l()+"/#"+n}function u(n){if(-1===n.indexOf("xmpCacheMode=")&&-1!==window.location.href.indexOf("xmpCacheMode=")){var e=new URL(n);return-1!==window.location.href.indexOf("xmpCacheMode=noCache")?e.searchParams.append("xmpCacheMode","noCache"):e.searchParams.append("xmpCacheMode","browser"),e.toString()}return n}a()&&(console.log=function(){for(var n=arguments.length,e=new Array(n),t=0;t<n;t++)e[t]=arguments[t];if(e){for(var o=0;o<e.length;o++)window.xmpCall("log",{msg:r(e[o])},null);c.apply(void 0,e)}});try{var d=navigator.userAgent.match(/timestamp:.*?[;|)]/g);if(d&&window.xmpFirstPaint&&window.xmpFirstScreen){var p=d[0].substring(10,d[0].length-1),f=window.xmpFirstPaint-parseFloat(p),g=window.xmpFirstScreen-parseFloat(p),m="";window.location.port&&(m=":"+window.location.port);var w=window.location.pathname;w.endsWith("/")&&(w=w.substring(0,w.length-1)),window.xmpCall("report",{tgt_event_id:"xmp_firstPaint",tgt_name:"firstPaint",num1:f,url:window.location.href,page:window.location.host+m+w},(function(n){})),window.xmpCall("report",{tgt_event_id:"xmp_firstScreen",tgt_name:"firstScreen",num1:g,url:window.location.href,page:window.location.host+m+w},(function(n){}))}}catch(h){console.log(h)}var _={openContainer:function(n){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];n=u(n),window.xmpCall("openContainer",{replace:e,url:n},(function(n){}))},openContainerByPath:function(n){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=s(n);t=u(t),window.xmpCall("openContainer",{replace:e,url:t},(function(n){}))},closeContainer:function(n){window.xmpCall("closeContainer",n||{},(function(n){}))},dismissBackButton:function(){window.xmpCall("dismissBackButton",{},(function(n){}))},onShow:function(n){a()?window.xmpOn("onShow",n):window.xmpOn("activedPage",n)},onHide:function(n){a()?window.xmpOn("onHide",n):window.xmpOn("leavePage",n)},onBackPress:function(n){a()?window.xmpOn("onBackPress",n):window.xmpOn("interceptBackPress",n)},on:window.xmpOn,call:window.xmpCall};e.xmp=_},"56d7":function(n,e,t){"use strict";var o=t("4ea4").default,i=o(t("5530"));t("e260"),t("e6cf"),t("cca6"),t("a79d"),t("6cdc"),t("1c31");var a=o(t("e143")),r=o(t("3dfd"));a.default.config.productionTip=!1,r.default.mpType="app";var c=new a.default((0,i.default)({},r.default));c.$mount()},"6cdc":function(n,e,t){"use strict";(function(n){var e=t("4ea4").default;t("13d5"),t("d3b7"),t("ddb0"),t("ac1f"),t("5319");var o=e(t("e143")),i={keys:function(){return[]}};n["________"]=!0,delete n["________"],n.__uniConfig={globalStyle:{navigationBarTextStyle:"black",navigationBarTitleText:"xmp",navigationBarBackgroundColor:"#F8F8F8",backgroundColor:"#F8F8F8"}},n.__uniConfig.compilerVersion="3.4.18",n.__uniConfig.uniPlatform="h5",n.__uniConfig.appId="",n.__uniConfig.appName="",n.__uniConfig.appVersion="1.0.0",n.__uniConfig.appVersionCode="100",n.__uniConfig.router={mode:"hash",base:"./"},n.__uniConfig.publicPath="./",n.__uniConfig["async"]={loading:"AsyncLoading",error:"AsyncError",delay:200,timeout:6e4},n.__uniConfig.debug=!1,n.__uniConfig.networkTimeout={request:6e4,connectSocket:6e4,uploadFile:6e4,downloadFile:6e4},n.__uniConfig.sdkConfigs={},n.__uniConfig.qqMapKey=void 0,n.__uniConfig.googleMapKey=void 0,n.__uniConfig.locale="",n.__uniConfig.fallbackLocale=void 0,n.__uniConfig.locales=i.keys().reduce((function(n,e){var t=e.replace(/\.\/(uni-app.)?(.*).json/,"$2"),o=i(e);return Object.assign(n[t]||(n[t]={}),o.common||o),n}),{}),n.__uniConfig.nvue={"flex-direction":"column"},n.__uniConfig.__webpack_chunk_load__=t.e,o.default.component("pages-index-index",(function(n){var e={component:t.e("pages-index-index").then(function(){return n(t("f75a"))}.bind(null,t)).catch(t.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(e.loading={name:"SystemAsyncLoading",render:function(n){return n(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(e.error={name:"SystemAsyncError",render:function(n){return n(__uniConfig["async"]["error"])}}),e})),o.default.component("pages-detail-detail",(function(n){var e={component:t.e("pages-detail-detail").then(function(){return n(t("3a5c"))}.bind(null,t)).catch(t.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(e.loading={name:"SystemAsyncLoading",render:function(n){return n(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(e.error={name:"SystemAsyncError",render:function(n){return n(__uniConfig["async"]["error"])}}),e})),o.default.component("pages-detail-detail-edit",(function(n){var e={component:t.e("pages-detail-detail-edit").then(function(){return n(t("0e40"))}.bind(null,t)).catch(t.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(e.loading={name:"SystemAsyncLoading",render:function(n){return n(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(e.error={name:"SystemAsyncError",render:function(n){return n(__uniConfig["async"]["error"])}}),e})),o.default.component("pages-test-screen-collector",(function(n){var e={component:t.e("pages-test-screen-collector").then(function(){return n(t("1e0e"))}.bind(null,t)).catch(t.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(e.loading={name:"SystemAsyncLoading",render:function(n){return n(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(e.error={name:"SystemAsyncError",render:function(n){return n(__uniConfig["async"]["error"])}}),e})),o.default.component("pages-test-koo-plugin",(function(n){var e={component:t.e("pages-test-koo-plugin").then(function(){return n(t("3267"))}.bind(null,t)).catch(t.oe),delay:__uniConfig["async"].delay,timeout:__uniConfig["async"].timeout};return __uniConfig["async"]["loading"]&&(e.loading={name:"SystemAsyncLoading",render:function(n){return n(__uniConfig["async"]["loading"])}}),__uniConfig["async"]["error"]&&(e.error={name:"SystemAsyncError",render:function(n){return n(__uniConfig["async"]["error"])}}),e})),n.__uniRoutes=[{path:"/",alias:"/pages/index/index",component:{render:function(n){return n("Page",{props:Object.assign({isQuit:!0,isEntry:!0},__uniConfig.globalStyle,{navigationStyle:"custom"})},[n("pages-index-index",{slot:"page"})])}},meta:{id:1,name:"pages-index-index",isNVue:!1,maxWidth:0,pagePath:"pages/index/index",isQuit:!0,isEntry:!0,windowTop:0}},{path:"/pages/detail/detail",component:{render:function(n){return n("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationStyle:"custom"})},[n("pages-detail-detail",{slot:"page"})])}},meta:{name:"pages-detail-detail",isNVue:!1,maxWidth:0,pagePath:"pages/detail/detail",windowTop:0}},{path:"/pages/detail/detail-edit",component:{render:function(n){return n("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"详情编辑页"})},[n("pages-detail-detail-edit",{slot:"page"})])}},meta:{name:"pages-detail-detail-edit",isNVue:!1,maxWidth:0,pagePath:"pages/detail/detail-edit",windowTop:44}},{path:"/pages/test/screen-collector",component:{render:function(n){return n("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"测试WebSocket"})},[n("pages-test-screen-collector",{slot:"page"})])}},meta:{name:"pages-test-screen-collector",isNVue:!1,maxWidth:0,pagePath:"pages/test/screen-collector",windowTop:44}},{path:"/pages/test/koo-plugin",component:{render:function(n){return n("Page",{props:Object.assign({},__uniConfig.globalStyle,{navigationBarTitleText:"测试WebSocket"})},[n("pages-test-koo-plugin",{slot:"page"})])}},meta:{name:"pages-test-koo-plugin",isNVue:!1,maxWidth:0,pagePath:"pages/test/koo-plugin",windowTop:44}},{path:"/preview-image",component:{render:function(n){return n("Page",{props:{navigationStyle:"custom"}},[n("system-preview-image",{slot:"page"})])}},meta:{name:"preview-image",pagePath:"/preview-image"}},{path:"/choose-location",component:{render:function(n){return n("Page",{props:{navigationStyle:"custom"}},[n("system-choose-location",{slot:"page"})])}},meta:{name:"choose-location",pagePath:"/choose-location"}},{path:"/open-location",component:{render:function(n){return n("Page",{props:{navigationStyle:"custom"}},[n("system-open-location",{slot:"page"})])}},meta:{name:"open-location",pagePath:"/open-location"}}],n.UniApp&&new n.UniApp}).call(this,t("c8ba"))},ea0b:function(n,e,t){var o=t("24fb");e=o(!1),e.push([n.i,"\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/*每个页面公共css */",""]),n.exports=e},ef9e:function(n,e,t){var o=t("ea0b");"string"===typeof o&&(o=[[n.i,o,""]]),o.locals&&(n.exports=o.locals);var i=t("4f06").default;i("d88fd794",o,!0,{sourceMap:!1,shadowMode:!1})},fd5e:function(n,e,t){"use strict";var o;t.d(e,"b",(function(){return i})),t.d(e,"c",(function(){return a})),t.d(e,"a",(function(){return o}));var i=function(){var n=this,e=n.$createElement,t=n._self._c||e;return t("App",{attrs:{keepAliveInclude:n.keepAliveInclude}})},a=[]}});