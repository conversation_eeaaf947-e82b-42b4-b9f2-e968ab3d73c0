(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-test-screen-collector"],{"05ac":function(e,t,n){"use strict";var o;n.d(t,"b",(function(){return c})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return o}));var c=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"content"},[n("v-uni-image",{staticClass:"logo",attrs:{src:"/static/logo.png"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.webSocketTest.apply(void 0,arguments)}}}),n("v-uni-view",[n("v-uni-button",{attrs:{type:"default"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.test.apply(void 0,arguments)}}},[e._v("测试完整流程")]),n("v-uni-button",{attrs:{type:"default"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openRTMP.apply(void 0,arguments)}}},[e._v("调用RTMP插件：openRTMP")]),n("v-uni-button",{attrs:{type:"default"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closeRTMP.apply(void 0,arguments)}}},[e._v("调用RTMP插件：closeRTMP")])],1)],1)},a=[]},"1e0e":function(e,t,n){"use strict";n.r(t);var o=n("05ac"),c=n("db0c");for(var a in c)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return c[e]}))}(a);n("fd49");var i,l=n("f0c5"),u=Object(l["a"])(c["default"],o["b"],o["c"],!1,null,"86041356",null,!1,o["a"],i);t["default"]=u.exports},4223:function(e,t,n){var o=n("5a2b");"string"===typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);var c=n("4f06").default;c("72951850",o,!0,{sourceMap:!1,shadowMode:!1})},"5a2b":function(e,t,n){var o=n("24fb");t=o(!1),t.push([e.i,".content[data-v-86041356]{display:flex;flex-direction:column;align-items:center;justify-content:center}.logo[data-v-86041356]{height:100px;width:100px;margin:100px auto 25px auto}.text-area[data-v-86041356]{display:flex;justify-content:center}.title[data-v-86041356]{font-size:18px;color:#8f8f94}",""]),e.exports=t},"643e":function(e,t,n){"use strict";function o(e,t,n,o){var c=new WebSocket(e);c.onmessage=function(e){console.log("收到消息..."),console.log(e);var o=JSON.parse(e.data);switch(o.method){case"authenticationSuccess":window.xmpCall("openRTMP",{url:t},(function(e){e&&1===e.code?c.send(JSON.stringify({method:"collect",data:{type:n}})):c.close()}));break;case"voice":new Audio(o.data.url).play();break;default:console.log("Sorry, we are out of this message.")}},c.onclose=function(e){window.xmpCall("closeRTMP",{},null),console.log("连接已关闭..."),console.log(e);var t=e.reason,n=e.code;try{var c=JSON.parse(e.reason);"collect"===c.method&&1===c.data.code&&(t=c.data.items,n=c.data.code)}catch(a){}o({code:n,value:t})}}n("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.initScreenCollector=o,n("e9c4")},b8b3:function(e,t,n){"use strict";n("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n("643e"),c=(n("4de5"),{data:function(){return{title:"该页面是帮助录屏采集平台测试WebSocket用的"}},onLoad:function(){},methods:{openRTMP:function(){window.xmpCall("openRTMP",{url:"rtmp://172.20.121.193:1935/defaultApp/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9~eyJpYXQiOjE2NTc4NjcwNDAsImV4cCI6MTY1Nzg2Nzk0MH0~PmQ6jXg-pHZEmR3R_QKnefYOX6FE3mwGS4-5pMOF5qg"},(function(e){console.log(e)}))},closeRTMP:function(){window.xmpCall("closeRTMP",{},null)},test:function(){(0,o.initScreenCollector)("ws://172.20.121.193:8023?token=123","rtmp://172.20.123.86:1935/defaultApp/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE2NTc4NjcwNDAsImV4cCI6MTY1Nzg2Nzk0MH0.PmQ6jXg-pHZEmR3R_QKnefYOX6FE3mwGS4-5pMOF5qg","wechatScore",(function(e){console.log(e)}))}}});t.default=c},db0c:function(e,t,n){"use strict";n.r(t);var o=n("b8b3"),c=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(a);t["default"]=c.a},fd49:function(e,t,n){"use strict";var o=n("4223"),c=n.n(o);c.a}}]);