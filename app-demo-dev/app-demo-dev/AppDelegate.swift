//
//  AppDelegate.swift
//  main2
//
//  Created by liumeng on 2021/4/12.
//
//

import UIKit

@main
class AppDelegate: UIResponder, UIApplicationDelegate {

    var window: UIWindow!


    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        window = UIWindow(frame: UIScreen.main.bounds)
        window.backgroundColor = .white
        let controller = DemoVC();
        //window.rootViewController = controller
        window.rootViewController = UINavigationController(rootViewController: controller)
        window.makeKeyAndVisible()
        return true
    }
}
