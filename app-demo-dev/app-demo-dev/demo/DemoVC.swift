//
// Created by lium<PERSON> on 2021/4/15.
//

import Foundation
import UIKit
import framework_xmp
import MobileCoreServices

class DemoVC: UIViewController, UIViewControllerTransitioningDelegate {

    override func viewDidLoad() {
        super.viewDidLoad()
        let button = UIButton(frame: CGRect(x: 10, y: 150, width: 100, height: 30))
        button.backgroundColor = .black
        button.setTitle("打开", for: .normal)
        button.addTarget(self, action: #selector(tapped(_:)), for: .touchUpInside)
        self.view.addSubview(button)


//        let cacheUrlObj = URL(string: "http://baidu.com")!
//        let cacheUrlObj2 = URL(string: "http://baidu.com/")!
//        let cacheUrlObj3 = URL(string: "http://baidu.com/#")!
//        let cacheUrlObj4 = URL(string: "http://baidu.com/aa")!
//        let cacheUrlObj5 = URL(string: "http://baidu.com/bb.html")!
//        let cacheUrlObj6 = URL(string: "http://baidu.com/cc/dd.html")!
//        let cacheUrlObj7 = URL(string: "http://baidu.com/ee/ff")!
//        let cacheUrlObj8 = URL(string: "http://baidu.com/ee/ff/gg.hh.html")!
//
//        var obj = cacheUrlObj
//        print(obj.absoluteString)
//        var type = "html"
//        var name = "index"
//        var subPath = ""
//        if let lastSlashIndex = obj.path.lastIndex(of: "/") {
//            let lastPath = obj.path.substring(from: lastSlashIndex);
//            if let lastPointIndex = lastPath.lastIndex(of: ".") {
//                type = lastPath.substring(from: String.Index(encodedOffset: lastPointIndex.encodedOffset + 1));
//                name = lastPath.substring(with: String.Index(encodedOffset: 1)..<lastPointIndex);
//                subPath = obj.path.substring(to: lastSlashIndex);
//            } else {
//                subPath = obj.path;
//                if subPath == "/" {
//                    subPath = ""
//                }
//            }
//        }
//        print("type:" + type)
//        print("name:" + name)
//        print("subPath:" + subPath)

        //TODO 规范化 mimeType
        func mimeTypeForPath(path: String) -> String {
            let url = NSURL(fileURLWithPath: path)
            let pathExtension = url.pathExtension

            if let uti = UTTypeCreatePreferredIdentifierForTag(kUTTagClassFilenameExtension, pathExtension! as NSString, nil)?.takeRetainedValue() {
                if let mimetype = UTTypeCopyPreferredTagWithClass(uti, kUTTagClassMIMEType)?.takeRetainedValue() {
                    return mimetype as String
                }
            }
            return "application/octet-stream"
        }

        //

        let urlDemos:[String] = ["http://baidu.com",
                                 "http://baidu.com/",
                                 "http://baidu.com/aa",
                                 "http://baidu.com/aa/",
                                 "http://baidu.com/aa/bb",
                                 "http://baidu.com/aa/bb/",
                                 "http://baidu.com:8080",
                                 "http://baidu.com:8080/",
                                 "http://baidu.com:8080/aa.js",
                                 "http://baidu.com:8080/aa/",
                                 "http://baidu.com:8080/aa/bb",
                                 "http://baidu.com:8080/aa/bb/"
        ]
        let str = "<!DOCTYPE html>\n<html lang=\"vi\">\n\n<head>\n    <meta http-equiv=\"Content-type\" content=\"text/html; charset=utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no\">\n    <meta name=\"format-detection\" content=\"telephone=no\">\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n    <meta http-equiv=\"pragma\" content=\"no-store\">\n    <meta http-equiv=\"Cache-Control\" content=\"no-store, must-revalidate\">\n    <meta name=\"apple-mobile-web-app-capable\" content=\"no\">\n    <meta http-equiv=\"expires\" content=\"0\">\n    <link rel=\"icon\" href=\"/loan/static/favicon.ico\">\n    <title></title>\n    <link href=\"/loan/css/app.0a8206b6.css\" rel=\"stylesheet\">\n</head>\n\n<body><noscript><strong>We're sorry but web-customer-main doesn't work properly without JavaScript enabled. Please\n            enable it to continue.</strong></noscript>\n    <div id=\"app\"></div>\n    <div id=\"portal-target\"></div>\n    <script src=\"//privacy.juanhand.com/libs/vue.runtime.min.js\"></script>\n    <script src=\"//privacy.juanhand.com/libs/vue-router.min.js\"></script>\n    <script src=\"//privacy.juanhand.com/libs/eruda.min.js\"></script>\n    <script>eruda.init();</script>\n    <script src=\"/loan/js/chunk-vendors.8fbb45a0.js\"></script>\n    <script src=\"loan/js/app.e4f6de06.js\"></script>\n    <script src=\"./loan/js/uni.e4f6de06.js\"></script>\n</body>\n\n</html>";

        let update = XMPUpdateManager()

        for url in urlDemos {
            let urlObj = URL(string:url)!;
            print(urlObj.path)
            print(urlObj.pathExtension.count)
            print(urlObj.deletingLastPathComponent().absoluteString)
//            let urls = update.getAllResourcesUrl(url: url, str: str);
//            print(url)
//            print(urls)
        }


    }

    @objc func tapped(_ button: UIButton) {
        //添加自定义插件
        class ReportPlugin: Plugin {
            func getName() -> String {
                "report";
            }

            func exec(viewController: XMPVC, data: NSDictionary, callback: PluginCallback) {
                print("dddddddd==============\"======")
                print(data)
                let nsDictionary: NSDictionary = ["key1": 123,"key2": "value1","key3": "vaue1"]
                callback.complete(data: nsDictionary)
            }
        }
        
        PluginManager.shared.addPlugin(xmpPlugin: ReportPlugin())
        //进入XMP
        let controller = XMPVC.init(urlStr: "https://www.google.com");
        //controller.hiddenBack = true
        present(controller, animated: true, completion: nil)
    }

    func regexGetSub(pattern: String, str: String) -> [String] {
        var subStr = [String]()
        let regex = try! NSRegularExpression(pattern: pattern, options: [])
        let matches = regex.matches(in: str, options: [], range: NSRange(str.startIndex..., in: str))
        //解析出子串
        for match in matches {
            //        subStr.append(String(str[Range(match.range(at: 1), in: str)!]))
            //        subStr.append(String(str[Range(match.range(at: 2), in: str)!]))
            subStr.append(contentsOf: [String(str[Range(match.range(at: 1), in: str)!]), String(str[Range(match.range(at: 2), in: str)!])])
        }
        return subStr
    }

    /**
     测试用例
     1、初次安装后，关闭网络，测试是否正常加载预置包
     2、更新H5站点
     3、打开网络看是否可以更新到新版H5
     4、更新H5站点，修改某个JS文件名
     5、打开网络看是否可以更新到新版H5
     6、重新打开页面看H5是新的还是旧的
     6、还原某个JS文件名
     7、打开网络看是否可以更新到新版H5
     8、关闭网络，杀掉App，重新打开App看H5情况
     */

}
