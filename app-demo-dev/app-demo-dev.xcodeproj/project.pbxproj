// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		87748CAD2AE26A36002E03D0 /* framework_xmp.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 87748CAC2AE26A36002E03D0 /* framework_xmp.framework */; };
		87748CAE2AE26A36002E03D0 /* framework_xmp.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 87748CAC2AE26A36002E03D0 /* framework_xmp.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		87CA2764287AC25000E87979 /* assets in Resources */ = {isa = PBXBuildFile; fileRef = 87CA2762287AC24F00E87979 /* assets */; };
		AF1AB3FF6D063951DE4547D6 /* DemoVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF1AB3B98238DA7274464C95 /* DemoVC.swift */; };
		AF1ABDADAB3ADEDE1C10F75B /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF1AB7CFA7493F104572FE5D /* AppDelegate.swift */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		87748CAB2AE269C5002E03D0 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				87748CAE2AE26A36002E03D0 /* framework_xmp.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		87748CAC2AE26A36002E03D0 /* framework_xmp.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = framework_xmp.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		87CA2762287AC24F00E87979 /* assets */ = {isa = PBXFileReference; lastKnownFileType = folder; path = assets; sourceTree = "<group>"; };
		AF1AB3B98238DA7274464C95 /* DemoVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DemoVC.swift; sourceTree = "<group>"; };
		AF1AB7CFA7493F104572FE5D /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		AF1AB853A138D08CA163D649 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.info; path = Info.plist; sourceTree = "<group>"; };
		AF1ABB4B7753F4135F22E526 /* app-demo-dev.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "app-demo-dev.app"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		AF1AB841AAC2F3B19BF213E8 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				87748CAD2AE26A36002E03D0 /* framework_xmp.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		87748CA42AE26943002E03D0 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				87748CAC2AE26A36002E03D0 /* framework_xmp.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		AF1AB2BA668EF666C38C50B8 /* demo */ = {
			isa = PBXGroup;
			children = (
				AF1AB3B98238DA7274464C95 /* DemoVC.swift */,
			);
			path = demo;
			sourceTree = "<group>";
		};
		AF1AB6D33631308FA261506D /* Products */ = {
			isa = PBXGroup;
			children = (
				AF1ABB4B7753F4135F22E526 /* app-demo-dev.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		AF1AB6F7CE4117F1B90BEF51 /* app-demo-dev */ = {
			isa = PBXGroup;
			children = (
				87CA2762287AC24F00E87979 /* assets */,
				AF1AB2BA668EF666C38C50B8 /* demo */,
				AF1AB7CFA7493F104572FE5D /* AppDelegate.swift */,
				AF1AB853A138D08CA163D649 /* Info.plist */,
			);
			path = "app-demo-dev";
			sourceTree = "<group>";
		};
		AF1ABA8A8F7229105A7CD89D = {
			isa = PBXGroup;
			children = (
				AF1AB6F7CE4117F1B90BEF51 /* app-demo-dev */,
				AF1AB6D33631308FA261506D /* Products */,
				87748CA42AE26943002E03D0 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		AF1AB6E2F0678FB7EBB91F21 /* app-demo-dev */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AF1ABB97D15E13A10BD8F6AB /* Build configuration list for PBXNativeTarget "app-demo-dev" */;
			buildPhases = (
				AF1AB4C90EC167A41B42AE8D /* Sources */,
				AF1AB841AAC2F3B19BF213E8 /* Frameworks */,
				AF1ABAC1994E4CF6550DBC65 /* Resources */,
				87748CAB2AE269C5002E03D0 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "app-demo-dev";
			productName = main2;
			productReference = AF1ABB4B7753F4135F22E526 /* app-demo-dev.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		AF1AB06853AAD07C410A9E4D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				TargetAttributes = {
					AF1AB6E2F0678FB7EBB91F21 = {
						DevelopmentTeam = SU25TSGG47;
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = AF1AB8F029D8A208017CEC19 /* Build configuration list for PBXProject "app-demo-dev" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
				Base,
			);
			mainGroup = AF1ABA8A8F7229105A7CD89D;
			productRefGroup = AF1AB6D33631308FA261506D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				AF1AB6E2F0678FB7EBB91F21 /* app-demo-dev */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		AF1ABAC1994E4CF6550DBC65 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				87CA2764287AC25000E87979 /* assets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		AF1AB4C90EC167A41B42AE8D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AF1ABDADAB3ADEDE1C10F75B /* AppDelegate.swift in Sources */,
				AF1AB3FF6D063951DE4547D6 /* DemoVC.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		AF1AB12BA27B139D7E556B20 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = SU25TSGG47;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				INFOPLIST_FILE = "app-demo-dev/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "com.xinye.app-demo-dev";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VALIDATE_WORKSPACE = NO;
			};
			name = Debug;
		};
		AF1AB22ECE698F9DA0473A24 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		AF1ABF5B46532552FDAB4AD9 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_OPTIMIZATION_LEVEL = "-Owholemodule";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		AF1ABFA6FDD9E9E6F6BF1368 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = SU25TSGG47;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				INFOPLIST_FILE = "app-demo-dev/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "com.xinye.app-demo-dev";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VALIDATE_WORKSPACE = NO;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		AF1AB8F029D8A208017CEC19 /* Build configuration list for PBXProject "app-demo-dev" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AF1AB22ECE698F9DA0473A24 /* Debug */,
				AF1ABF5B46532552FDAB4AD9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AF1ABB97D15E13A10BD8F6AB /* Build configuration list for PBXNativeTarget "app-demo-dev" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AF1AB12BA27B139D7E556B20 /* Debug */,
				AF1ABFA6FDD9E9E6F6BF1368 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = AF1AB06853AAD07C410A9E4D /* Project object */;
}
