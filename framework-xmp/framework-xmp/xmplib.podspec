Pod::Spec.new do |s|
  s.name             = 'xmplib'
  s.version          = '1.2.9'
  s.summary          = 'A short description of xmplib'
  s.author           = { 'liumeng02' => '<EMAIL>' }
  s.homepage         = 'http://git.ppdaicorp.com/XMP/XMP_Container_iOS'

  s.source           = { :git => '*********************:XMP/XMP_Container_iOS.git', :tag => s.version.to_s }

  s.ios.deployment_target = '9.0'
  s.swift_version = '5.0'

  s.source_files = ['framework-xmp/framework-xmp/**/*.{h,m,swift}']
  s.exclude_files = [ 'framework-xmp/framework-xmp/Info.plist' ]
  s.resources     = "framework-xmp/framework-xmp/framework-xmp.bundle"
  s.public_header_files = 'framework-xmp/framework-xmp/*.h'
  s.frameworks = 'UIKit', 'Foundation','WebKit'
  # s.dependency 'AFNetworking', '~> 2.3'
end

