import UIKit
import WebKit

public class XMPCacheManager: NSObject {
    @objc public static let shared = XMPCacheManager()
    static let IOS_TTP_SCHEME: String = "iosttp"
    var urlSession: URLSession?

    override private init() {
        super.init()
        let config = URLSessionConfiguration.default
        config.urlCache = URLCache(memoryCapacity: 20 * 1024 * 1024, diskCapacity: 50 * 1024 * 1024, diskPath: "xmp")
        urlSession = URLSession(configuration: config)
    }

    // 主文档加载逻辑
    @objc public func requestReturnCacheDataElseLoad(url: String, webView: WKWebView) {
        if let urlObj = URL(string: url),
           var scheme = urlObj.scheme,
           let host = urlObj.host {
            // 定制了scheme的url
            let orIOSTTPUrl = XMPCacheManager.shared.iosttpUrl(url: url)
            // 做网络请求时，将自定义scheme还原成http
            scheme = scheme.replacingOccurrences(of: XMPCacheManager.IOS_TTP_SCHEME, with: "http")
            // 初始化cacheUrl
            var cacheUrl: String
            if let port = urlObj.port {
                cacheUrl = scheme + "://" + host + ":" + String(port) + urlObj.path
            } else {
                cacheUrl = scheme + "://" + host + urlObj.path
            }
            let cacheUrlObj = URL(string: cacheUrl)!
            // 1、先走缓存
            var request = URLRequest(url: cacheUrlObj)
            request.httpMethod = "GET"
            request.cachePolicy = .returnCacheDataDontLoad
            XMPCacheManager.shared.urlSession!.dataTask(with: request) { data, response, _ in
                if let httpResponse = response as? HTTPURLResponse,
                   httpResponse.statusCode == 200,
                   let data = data,
                   let str = String(data: data, encoding: String.Encoding.utf8) {
                    print("XMP:主文档 走缓存操作 成功：" + cacheUrl)
                    if XMPCacheManager.shared.isH5DevEnv(content: str) {
                        XMPCacheManager.shared.loadUrlIgnoreCache(webView: webView, orIOSTTPUrl: orIOSTTPUrl)
                    } else {
                        DispatchQueue.main.async {
                            // baseURL尽可能使用自定义scheme
                            webView.loadHTMLString(str, baseURL: URL(string: orIOSTTPUrl)!)
                            // 更新操作
                            XMPUpdateManager().update(mCacheUrl: cacheUrl, mOrIOSTTPUrl: orIOSTTPUrl, oldStr: str, weak: webView)
                        }
                    }
                } else {
                    print("XMP:主文档 走缓存操作 失败：" + cacheUrl)
                    // 2 走预置包
                    if let path = self.getAssetsFilePath(url: cacheUrlObj) {
                        XMPCacheManager.shared.urlSession!.dataTask(with: URLRequest(url: URL(fileURLWithPath: path))) { data, _, _ in
                            if let data = data,
                               data.count > 0,
                               let str = String(data: data, encoding: String.Encoding.utf8) {
                                print("XMP:主文档 走预置包操作 成功：" + cacheUrl)
                                if XMPCacheManager.shared.isH5DevEnv(content: str) {
                                    XMPCacheManager.shared.loadUrlIgnoreCache(webView: webView, orIOSTTPUrl: orIOSTTPUrl)
                                } else {
                                    DispatchQueue.main.async {
                                        // baseURL尽可能使用自定义scheme
                                        webView.loadHTMLString(str, baseURL: URL(string: orIOSTTPUrl)!)
                                        // 更新操作
                                        XMPUpdateManager().update(mCacheUrl: cacheUrl, mOrIOSTTPUrl: orIOSTTPUrl, oldStr: str, weak: webView)
                                    }
                                }
                            } else {
                                print("XMP:主文档 走预置包操作 失败：" + cacheUrl)
                                // 3 走网络
                                XMPCacheManager.shared.requestReloadIgnoringCacheData(cacheUrl: cacheUrl, orIOSTTPUrl: orIOSTTPUrl, webView: webView)
                            }
                        }.resume()
                    } else {
                        print("XMP:主文档 走预置包操作 失败：" + cacheUrl)
                        // 3 走网络
                        XMPCacheManager.shared.requestReloadIgnoringCacheData(cacheUrl: cacheUrl, orIOSTTPUrl: orIOSTTPUrl, webView: webView)
                    }
                }
            }.resume()
        }
    }

    func requestReloadIgnoringCacheData(cacheUrl: String, orIOSTTPUrl: String, webView: WKWebView) {
        let cacheUrlObj = URL(string: cacheUrl)!
        var request = URLRequest(url: cacheUrlObj)
        request.httpMethod = "GET"
        request.cachePolicy = .reloadIgnoringLocalAndRemoteCacheData
        XMPCacheManager.shared.urlSession!.dataTask(with: request) { data, response, _ in
            if let httpResponse = response as? HTTPURLResponse,
               httpResponse.statusCode == 200,
               let data = data,
               let str = String(data: data, encoding: String.Encoding.utf8) {
                print("XMP:主文档 走网络操作 成功：" + cacheUrl)
                if XMPCacheManager.shared.isH5DevEnv(content: str) {
                    XMPCacheManager.shared.loadUrlIgnoreCache(webView: webView, orIOSTTPUrl: orIOSTTPUrl)
                } else {
                    // 走网络成功后 加载主文档
                    DispatchQueue.main.async {
                        // baseURL尽可能使用自定义scheme
                        webView.loadHTMLString(str, baseURL: URL(string: orIOSTTPUrl)!)
                    }
                }
            } else {
                print("XMP:主文档 走网络操作 失败：" + cacheUrl)
                XMPCacheManager.shared.loadUrlIgnoreCache(webView: webView, orIOSTTPUrl: orIOSTTPUrl)
            }
        }.resume()
    }

    @objc public func setWKURLSchemeHandler(config: WKWebViewConfiguration) {
        if #available(iOS 11.0, *) {
            let schemeHandler = XMPWKURLSchemeHandler()
            config.setURLSchemeHandler(schemeHandler, forURLScheme: XMPCacheManager.IOS_TTP_SCHEME)
            config.setURLSchemeHandler(schemeHandler, forURLScheme: XMPCacheManager.IOS_TTP_SCHEME + "s")
        }
    }

    func httpUrl(url: String) -> String {
        let urlCom = URLComponents(url: URL(string: url)!, resolvingAgainstBaseURL: true)
        var localUrlCom = urlCom
        localUrlCom?.scheme = urlCom?.scheme?.replacingOccurrences(of: XMPCacheManager.IOS_TTP_SCHEME, with: "http")
        return localUrlCom?.url?.absoluteString ?? url
    }

    func iosttpUrl(url: String) -> String {
        if #available(iOS 11.0, *) {
            let urlCom = URLComponents(url: URL(string: url)!, resolvingAgainstBaseURL: true)
            var localUrlCom = urlCom
            localUrlCom?.scheme = urlCom?.scheme?.replacingOccurrences(of: "http", with: XMPCacheManager.IOS_TTP_SCHEME)
            return localUrlCom?.url?.absoluteString ?? url
        }
        return url
    }

    func getAssetsFilePath(url: URL) -> String? {
        var type = "html"
        var name = "index"
        var subPath = ""
        if let lastSlashIndex = url.path.lastIndex(of: "/") {
            let lastPath = url.path.substring(from: lastSlashIndex)
            if let lastPointIndex = lastPath.lastIndex(of: ".") {
                type = lastPath.substring(from: String.Index(encodedOffset: lastPointIndex.encodedOffset + 1))
                name = lastPath.substring(with: String.Index(encodedOffset: 1) ..< lastPointIndex)
                subPath = url.path.substring(to: lastSlashIndex)
            } else {
                subPath = url.path
                if subPath == "/" {
                    subPath = ""
                }
            }
        }
        var directory: String
        if let port = url.port {
            directory = "assets/xmp/" + url.host! + "_" + String(port) + subPath
        } else {
            directory = "assets/xmp/" + url.host! + subPath
        }
        return Bundle.main.path(forResource: name, ofType: type, inDirectory: directory)
    }

    func loadUrlIgnoreCache(webView: WKWebView, orIOSTTPUrl: String) {
        // H5开发模式 或者 失败以后直接重新loadUrl，虽然大概率还是失败但可以触发webview的Error回调，使用reloadIgnoringLocalAndRemoteCacheData确保强缓存模式可以完全不依赖H5站点的ng配置
        let httpUrlString = XMPCacheManager.shared.httpUrl(url: orIOSTTPUrl)
        var request = URLRequest(url: URL(string: httpUrlString)!)
        request.cachePolicy = .reloadIgnoringLocalAndRemoteCacheData
        DispatchQueue.main.async {
            webView.load(request)
        }
    }

    func isH5DevEnv(content: String) -> Bool {
        // 是开发模式，则强制使用无缓存模式，chunk-vendors.js是vue，bundle.js是react，main.ts是vue3
        return content.contains("chunk-vendors.js") || content.contains("bundle.js") || content.contains("main.ts")
    }
}

// 只有主文档中 以相对路径开头的资源文件 才会自动带上iosttp或者iosttps scheme，才会走到下边逻辑
@available(iOS 11.0, *)
class XMPWKURLSchemeHandler: NSObject, WKURLSchemeHandler {
    var holdUrlSchemeTasks = [AnyHashable: Bool]()
    func webView(_ webView: WKWebView, start urlSchemeTask: WKURLSchemeTask) {
        holdUrlSchemeTasks[urlSchemeTask.description] = true
        if let customUrl = urlSchemeTask.request.url,
           let httpMethod = urlSchemeTask.request.httpMethod {
            let originUrl = customUrl.absoluteString.replacingOccurrences(of: XMPCacheManager.IOS_TTP_SCHEME, with: "http")
            if let originUrlObj = URL(string: originUrl) {
                var request = URLRequest(url: originUrlObj)
                // 不是get请求就认为是接口请求，直接走网络 ，没有后缀或者后缀包含htm 直接走网络
                if httpMethod.lowercased() != "get" || originUrlObj.pathExtension.count == 0 || originUrlObj.pathExtension.contains("htm") {
                    request = copyRequest(from: urlSchemeTask.request, to: request)
                    getFromRemote(request: request, url: originUrl, urlSchemeTask: urlSchemeTask)
                } else {
                    // 1、先走缓存
                    // 这里不能copyRequest，因为URLCache是按照完成的Request作为Key缓存的，urlSchemeTask.request的header中有变量，导致每次缓存成不同的Key
                    request.cachePolicy = .returnCacheDataDontLoad
                    XMPCacheManager.shared.urlSession!.dataTask(with: request) { [weak urlSchemeTask, self] data, response, _ in
                        if let httpResponse = response as? HTTPURLResponse,
                           httpResponse.statusCode == 200,
                           let data = data {
                            print("XMP:资源文件 走缓存操作 成功：" + originUrl)
                            didFinish(httpResponse: httpResponse, data: data, urlSchemeTask: urlSchemeTask)
                        } else {
                            print("XMP:资源文件 走缓存操作 失败：" + originUrl)
                            // 2、走预置包
                            if let path = XMPCacheManager.shared.getAssetsFilePath(url: originUrlObj) {
                                XMPCacheManager.shared.urlSession!.dataTask(with: URLRequest(url: URL(fileURLWithPath: path))) { [weak urlSchemeTask, self] data, _, _ in
                                    if let data = data, data.count > 0 {
                                        let headerFields = [
                                            // "Content-Type": String.stringNullDeal(with: response.mimeType),
                                            "Content-Length": String(data.count),
                                        ]
                                        let response = HTTPURLResponse(url: originUrlObj, statusCode: 200, httpVersion: "HTTP/1.1", headerFields: headerFields)
                                        print("XMP:资源文件 走预置包操作 成功：" + originUrl)
                                        didFinish(httpResponse: response!, data: data, urlSchemeTask: urlSchemeTask)
                                    } else {
                                        print("XMP:资源文件 走预置包操作 失败：" + originUrl)
                                        // 3 走网络
                                        getFromRemote(request: request, url: originUrl, urlSchemeTask: urlSchemeTask)
                                    }
                                }.resume()
                            } else {
                                print("XMP:资源文件 走预置包操作 失败：" + originUrl)
                                // 3 走网络
                                getFromRemote(request: request, url: originUrl, urlSchemeTask: urlSchemeTask)
                            }
                        }
                    }.resume()
                }
            }
        }
    }

    func webView(_ webView: WKWebView, stop urlSchemeTask: WKURLSchemeTask) {
        holdUrlSchemeTasks[urlSchemeTask.description] = false
    }

    func didFailWithError(domain: String, urlSchemeTask: WKURLSchemeTask?) {
        let error = NSError(domain: domain, code: 460)
        if let urlSchemeTask = urlSchemeTask,
           let isValid = holdUrlSchemeTasks[urlSchemeTask.description], isValid {
            do {
                try urlSchemeTask.didFailWithError(error)
            } catch {
                print(error.localizedDescription)
            }
        }
    }

    func didFinish(httpResponse: HTTPURLResponse, data: Data, urlSchemeTask: WKURLSchemeTask?) {
        if let urlSchemeTask = urlSchemeTask,
           let isValid = holdUrlSchemeTasks[urlSchemeTask.description], isValid {
            do {
                try urlSchemeTask.didReceive(httpResponse)
                try urlSchemeTask.didReceive(data)
                try urlSchemeTask.didFinish()
            } catch {
                print(error.localizedDescription)
            }
        }
    }

    func getFromRemote(request: URLRequest, url: String, urlSchemeTask: WKURLSchemeTask?) {
        // 出现非200的情况有两种：缓存里非200，没有用缓存用的网络但返回非200，但无论哪种情况都重新请求一遍
        // 3 强制走网络
        var mRequest = request
        mRequest.cachePolicy = .reloadIgnoringLocalAndRemoteCacheData
        XMPCacheManager.shared.urlSession!.dataTask(with: mRequest) { [weak urlSchemeTask, self] data, response, _ in
            if let httpResponse = response as? HTTPURLResponse,
               httpResponse.statusCode == 200,
               let data = data {
                print("XMP:资源文件 走网络操作 成功：" + url)
                didFinish(httpResponse: httpResponse, data: data, urlSchemeTask: urlSchemeTask)
            } else {
                print("XMP:资源文件 走网络操作 失败：" + url)
                didFailWithError(domain: url, urlSchemeTask: urlSchemeTask)
            }
        }.resume()
    }

    func copyRequest(from: URLRequest, to: URLRequest) -> URLRequest {
        var toRequest = to
        toRequest.httpMethod = from.httpMethod
        toRequest.allHTTPHeaderFields = from.allHTTPHeaderFields
        toRequest.httpBody = from.httpBody
        toRequest.httpBodyStream = from.httpBodyStream
        return toRequest
    }
}
