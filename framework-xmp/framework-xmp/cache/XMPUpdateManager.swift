import UIKit
import WebKit


public class XMPUpdateManager {
    let urlSession: URLSession;
    var batchRequestCount = 0;
    var batchRequestCountCurrent = 0;
    var batchRequestResult = true;
    var cacheUrl = ""
    var orIOSTTPUrl = ""
    weak var webView: WKWebView?;

    public init() {
        let config = URLSessionConfiguration.default
        config.urlCache = URLCache(memoryCapacity: 0, diskCapacity: 0, diskPath: "xmp-update")
        urlSession = URLSession(configuration: config)
    }

    public func update(mCacheUrl: String, mOrIOSTTPUrl: String, oldStr: String, weak mWebView: WKWebView) {
        webView = mWebView;
        cacheUrl = mCacheUrl;
        orIOSTTPUrl = mOrIOSTTPUrl;
        var request = URLRequest(url: URL(string: cacheUrl)!)
        request.httpMethod = "GET"
        request.cachePolicy = .reloadIgnoringLocalAndRemoteCacheData
        urlSession.dataTask(with: request) { [self] (data, response, error) in
                    if let httpResponse = response as? HTTPURLResponse,
                       httpResponse.statusCode == 200,
                       let data = data,
                       let str = String(data: data, encoding: String.Encoding.utf8) {
                        if (oldStr != str) {
                            print("XMP:主文档 判断操作 需要更新：" + cacheUrl)
                            batchRequest(getAllResourcesUrl(url: cacheUrl, str: str))
                        } else {
                            print("XMP:主文档 判断操作 无需更新：" + cacheUrl)
                        }
                    } else {
                        print("XMP:主文档 判断操作 失败：" + cacheUrl)
                    }
                }
                .resume()
    }

    /**
     * 支持 http // / ./ 空    开头的资源
     */
    public func getAllResourcesUrl(url: String, str: String) -> [String] {
        var urls: [String] = []
        let pattern = "(href|src)=\"([0-9a-zA-Z\\./\\-_]*)(js|css)\""
        if let regex = try? XMPRegex(pattern),
           let urlObj = URL(string: url),
           let scheme = urlObj.scheme,
           let host = urlObj.host {
            for match in regex.matches(in: str) {
                if var path = match.captures[1],
                   let suffix = match.captures[2] {
                    path = path + suffix;
                    if (path.starts(with: "http")) {
                        urls.append(path)
                    } else if (path.starts(with: "//")) {
                        urls.append(scheme + ":" + path)
                    } else if (path.starts(with: "/")) {
                        if let port = urlObj.port {
                            urls.append(scheme + "://" + host + ":" + String(port) + path);
                        } else {
                            urls.append(scheme + "://" + host + path);
                        }
                    } else if (path.starts(with: "./") || !path.starts(with: ".")) {
                        let newPath = path.starts(with: "./") ? path.substring(from: String.Index(encodedOffset: 2)) : path;
                        if (urlObj.path == "") {
                            urls.append(url + "/" + newPath)
                        } else if (urlObj.path == "/") {
                            urls.append(url + newPath)
                        } else {
                            urls.append(urlObj.deletingLastPathComponent().absoluteString + newPath)
                        }
                    }
                }
            }
        }
        return urls
// test case
//        let urlDemos:[String] = ["http://baidu.com",
//                                 "http://baidu.com/",
//                                 "http://baidu.com/aa",
//                                 "http://baidu.com/aa/",
//                                 "http://baidu.com/aa/bb",
//                                 "http://baidu.com/aa/bb/",
//                                 "http://baidu.com:8080",
//                                 "http://baidu.com:8080/",
//                                 "http://baidu.com:8080/aa",
//                                 "http://baidu.com:8080/aa/",
//                                 "http://baidu.com:8080/aa/bb",
//                                 "http://baidu.com:8080/aa/bb/"
//        ]
    }

    public func batchRequest(_ urls: [String]) {
        for url in urls {
            if let urlObj = URL(string: url) {
                batchRequestCount = batchRequestCount + 1;
            }
        }
        if (batchRequestCount == 0) {
            // 如果主文档没有找到js和css url则自己更新就好了
            batchRequestCount = 1;
            allResult(true);
            return
        }
        for url in urls {
            if let urlObj = URL(string: url) {
                var request = URLRequest(url: urlObj)
                request.httpMethod = "GET"
                request.cachePolicy = .returnCacheDataDontLoad // 这个地方为什么不用ElseLoad呢？因为担心缓存了非200的请求而不再重新发起网络了
                XMPCacheManager.shared.urlSession!.dataTask(with: request) { [self] (data, response, error) in
                            if let httpResponse = response as? HTTPURLResponse,
                               httpResponse.statusCode == 200,
                               let data = data,
                               let str = String(data: data, encoding: String.Encoding.utf8) {
                                allResult(true);
                                print("XMP:主文档-资源文件 更新操作 成功：" + url)
                            } else {
                                // 缓存没有走网络
                                var request = URLRequest(url: urlObj)
                                request.httpMethod = "GET"
                                request.cachePolicy = .reloadIgnoringLocalAndRemoteCacheData
                                XMPCacheManager.shared.urlSession!.dataTask(with: request) { [self] (data, response, error) in
                                            if let httpResponse = response as? HTTPURLResponse,
                                               httpResponse.statusCode == 200,
                                               let data = data,
                                               let str = String(data: data, encoding: String.Encoding.utf8) {
                                                allResult(true);
                                                print("XMP:主文档-资源文件 更新操作 成功：" + url)
                                            } else {
                                                print("XMP:主文档-资源文件 更新操作 失败：" + url)
                                                allResult(false);
                                            }
                                        }
                                        .resume()
                            }
                        }
                        .resume()
            }
        }
    }

    public func allResult(_ result: Bool) {
        batchRequestCountCurrent = batchRequestCountCurrent + 1;
        if (!result) {//有一个请求失败则认为失败
            batchRequestResult = false;
        }
        if (batchRequestCount == batchRequestCountCurrent) {//全部请求结束
            if (batchRequestResult) {
                var request = URLRequest(url: URL(string: cacheUrl)!)
                request.httpMethod = "GET"
                request.cachePolicy = .reloadIgnoringLocalAndRemoteCacheData
                XMPCacheManager.shared.urlSession!.dataTask(with: request) { [self] (data, response, error) in
                            if let httpResponse = response as? HTTPURLResponse,
                               httpResponse.statusCode == 200,
                               let data = data,
                               let str = String(data: data, encoding: String.Encoding.utf8) {
                                print("XMP:主文档 更新操作 成功：" + cacheUrl)
                                // 更新操作且需要更新 则webview重新load
                                DispatchQueue.main.async {
                                    //！！！！webview重新load时，使用loadHtmlString和loaddata都不生效，暂时未找到原因
                                    //！！！！有一点点影响：更新成功后，马上断网，则主文档和资源文件可能会加载不到，因为更新成功后的缓存在http，不在webview
                                    let httpUrlString = XMPCacheManager.shared.httpUrl(url: orIOSTTPUrl)
                                    var request = URLRequest(url: URL(string: httpUrlString)!)
                                    request.cachePolicy = .reloadIgnoringLocalAndRemoteCacheData
                                    webView?.load(request);
                                }
                            } else {
                                print("XMP:主文档 更新操作 失败：" + cacheUrl)
                            }
                        }
                        .resume()
            } else {
                print("XMP:主文档 更新操作 失败：" + cacheUrl)
            }
        }
    }
}
