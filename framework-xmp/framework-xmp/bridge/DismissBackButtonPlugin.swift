//
// Created by l<PERSON>eng on 2021/4/15.
//

import Foundation
class DismissBackButtonPlugin : Plugin{
    func getName() -> String {
        "dismissBackButton";
    }

    func exec(viewController: XMPVC, data: NSDictionary, callback: PluginCallback)  {
        if let bacKView = viewController.backView {
            bacKView.removeFromSuperview()
        }
        callback.complete(data: [:])
    }


}
