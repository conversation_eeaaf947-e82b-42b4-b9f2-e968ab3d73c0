//
// Created by l<PERSON><PERSON> on 2021/4/13.
//

import Foundation

@objc public protocol Plugin {
    func getName() -> String
    func exec(viewController: XMPVC, data: NSDictionary, callback: PluginCallback)
}

public class PluginCallback: NSObject {
    let controller: XMPVC?;
    let dict: [String: Any]?;

    init(_ controller: XMPVC, _ dict: [String: Any]) {
        self.controller = controller;
        self.dict = dict;
        super.init()
    }

    @objc public func complete(data: NSDictionary) {
        if let controller = controller,
           let dict = dict,
           let method = dict["method"] as? String,
           let callbackId = dict["callbackId"] as? String {
            let dataJSObj = XMPUtil.dict2json(dict: data);
            controller.evaluateJavaScript(method: "onNativeCall", params: "{\"method\":\"" + method + "\",\"callbackId\":\"" + callbackId + "\",\"data\":" + dataJSObj + "}");
        }
    }
}
