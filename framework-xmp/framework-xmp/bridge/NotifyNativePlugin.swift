//
//  NotifyNativePlugin.swift
//  framework-xmp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/7/2.
//

import UIKit

class NotifyNativePlugin: Plugin {
    func getName() -> String {
        "notifyNative"
    }

    func exec(viewController: XMPVC, data: NSDictionary, callback: PluginCallback) {
        if let event = data["event"] as? String {
            let internalData = data["data"] as? NSDictionary
            NotificationCenter.default.post(name: NSNotification.Name(rawValue: event), object: internalData)
        }
       
    }
}
