//
// Created by lium<PERSON> on 2021/4/13.
//

import Foundation
class CloseContainerPlugin : Plugin{
    func getName() -> String {
        "closeContainer";
    }

    func exec(viewController: XMPVC, data: NSDictionary, callback: PluginCallback)  {
        viewController.resultDelegate?.onVCResult(data: data)
        if(viewController.navigationController != nil){
            viewController.navigationController?.popViewController(animated: true)
        }else{
            viewController.dismiss(animated: true, completion: nil);
        }
        callback.complete(data: [:])
    }


}
