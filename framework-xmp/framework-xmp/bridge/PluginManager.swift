import UIKit
import WebKit

class PluginManagerMessage: NSObject {
    var method: String
    var data: Dictionary<String, Any>
    init(method: String, data: Dictionary<String, Any>) {
        self.method = method
        self.data = data
    }
}

public class PluginManager: NSObject {
    @objc public static let shared = PluginManager()

    var dictionary = [String: Any]()


    private override init() {
        super.init()
        addPlugin(xmpPlugin: OpenContainerPlugin())
        addPlugin(xmpPlugin: CloseContainerPlugin())
        addPlugin(xmpPlugin: LogPlugin())
        addPlugin(xmpPlugin: DismissBackButtonPlugin())
        addPlugin(xmpPlugin: NotSupportedPlugin())
        addPlugin(xmpPlugin: GoBackToContainerPlugin())
        addPlugin(xmpPlugin: NotifyH5Plugin())
        addPlugin(xmpPlugin: NotifyNativePlugin())
        addPlugin(xmpPlugin: CloseAllPlugin())
    }

    @objc public func addPlugin(xmpPlugin: Plugin) {
        dictionary[xmpPlugin.getName()] = xmpPlugin;
    }

    func dispatch(message: WKScriptMessage, viewController: XMPVC) {
        let dict = XMPUtil.json2dict(text: message.body as? String)
        if let method = dict["method"] as? String {

            var data: NSDictionary = [:];
            if let tmpData = dict["data"] as? NSDictionary {
                data = tmpData;
            }

            var plugin = dictionary["notSupported"] as! Plugin
            if let tmpPlugin = dictionary[method] as? Plugin {
                plugin = tmpPlugin;
            }
            plugin.exec(viewController: viewController, data: data, callback: PluginCallback(viewController, dict))
        }
    }
    
    func dispatchMessage(_ message: PluginManagerMessage, _ viewController: XMPVC) {
        let plugin = dictionary[message.method] ?? dictionary["notSupported"]
        if let safePlugin = plugin as? Plugin {
            safePlugin.exec(viewController: viewController, data: message.data as NSDictionary, callback: PluginCallback(viewController, message.data))
        }
    }
}
