//
//  CloseAllPlugin.swift
//  framework-xmp
//
//  Created by ya<PERSON><PERSON><PERSON> on 2024/7/2.
//

import UIKit

class CloseAllPlugin: Plugin {
    func getName() -> String {
        "closeAll";
    }

    func exec(viewController: XMPVC, data: NSDictionary, callback: PluginCallback)  {
        if  let firstXmp =  XMPVCManager.shared.findFirstXmpVC(current : viewController) {
            if let nav = firstXmp.navigationController { // 有导航栈的场景
                if let index = nav.viewControllers.firstIndex(of: firstXmp) {
                    let targetVC = nav.viewControllers[max(0, index - 1)]
                    nav.popToViewController(targetVC, animated: true)
                } else {
                    nav.popToRootViewController(animated: true)
                }
            } else { // 无导航栈的场景
                if let parent = firstXmp.parent {
                    parent.dismiss(animated: true)
                } else {
                    firstXmp.dismiss(animated: true)
                }
            }
            callback.complete(data: [:])
        }
       
    }
}
