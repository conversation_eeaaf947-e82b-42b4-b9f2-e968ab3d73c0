//
// Created by l<PERSON>eng on 2021/4/15.
//

import Foundation

class NotSupportedPlugin: Plugin {
    func getName() -> String {
        "notSupported";
    }

    func exec(viewController: XMPVC, data: NSDictionary, callback: PluginCallback) {
        var result:NSMutableDictionary = [:]
        result["code"] = 404;
        result["msg"] = "notSupported";
        callback.complete(data: result)
    }

}