//
//  NotifyH5Plugin.swift
//  xmplib
//
//  Created by lium<PERSON> on 2023/3/21.
//

import Foundation
class NotifyH5Plugin: Plugin {
    func getName() -> String {
        "notifyH5"
    }

    func exec(viewController: XMPVC, data: NSDictionary, callback: PluginCallback) {
        if let event = data["event"] as? String {
            let internalData = data["data"] as? NSDictionary
            XMPVCManager.shared.notifyH5(event: event, data: internalData ?? [:])
        }
        callback.complete(data: [:])
    }
}
