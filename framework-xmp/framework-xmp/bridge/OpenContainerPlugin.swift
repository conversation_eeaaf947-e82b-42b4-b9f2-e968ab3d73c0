//
// Created by l<PERSON><PERSON> on 2021/4/13.
//

import Foundation

class OpenContainerPlugin: Plugin {
    func getName() -> String {
        "openContainer";
    }

    /**
     present只能逐级返回，push所有视图由视图栈控制，可以返回上一级，也可以返回到根vc，其他vc。
     - Parameters:
       - viewController:
       - data:
       - callback:
     */
    func exec(viewController: XMPVC, data: NSDictionary, callback: PluginCallback) {
        let controller = XMPVC()
        controller.url = data["url"] as? String
        controller.resultDelegate = viewController;
        let replace = data["replace"] as? Bool ?? false;
        if (replace) {
            controller.resultDelegate = viewController.resultDelegate;
            if (viewController.navigationController != nil) {
                let navigationController = viewController.navigationController;
                navigationController?.popViewController(animated: false)
                navigationController?.pushViewController(controller, animated: true)
            } else {
                let presentingViewController = viewController.presentingViewController;
                viewController.dismiss(animated: false, completion: nil);
                presentingViewController?.present(controller, animated: true, completion: nil)
            }
        } else {
            if (viewController.navigationController != nil) {
                viewController.navigationController?.pushViewController(controller, animated: true)
            } else {
                viewController.present(controller, animated: true, completion: nil)
            }
        }
        callback.complete(data: [:])
    }
}
