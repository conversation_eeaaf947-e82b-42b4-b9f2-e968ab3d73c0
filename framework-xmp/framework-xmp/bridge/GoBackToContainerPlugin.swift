//
//  GoBackToContainerPlugin.swift
//  xmplib
//
//  Created by lium<PERSON> on 2023/3/21.
//

import Foundation
class GoBackToContainerPlugin: Plugin {
    func getName() -> String {
        "goBackToContainer"
    }

    func exec(viewController: XMPVC, data: NSDictionary, callback: PluginCallback) {
        var patternOrNum:String?;
        if let patternOrNumInt = data["patternOrNum"] as? Int {
            patternOrNum = String(patternOrNumInt)
        } else if let patternOrNumString = data["patternOrNum"] as? String {
            patternOrNum = patternOrNumString
        }
        
        if patternOrNum != nil {
            let internalData = data["data"] as? NSDictionary
            XMPVCManager.shared.goBackToContainer(patternOrNum: patternOrNum!, data: internalData ?? [:])
        }
        
        callback.complete(data: [:])
    }
}
