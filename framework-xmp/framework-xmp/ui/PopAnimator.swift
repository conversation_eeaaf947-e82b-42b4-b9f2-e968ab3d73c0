//
// Created by liumeng on 2021/4/16.
//

import Foundation
import UIKit

class PopAnimator: NSObject,UIViewControllerAnimatedTransitioning{
    let duration = 0.3

    func transitionDuration(using transitionContext: UIViewControllerContextTransitioning?) -> TimeInterval {
        return duration
    }

    func animateTransition(using transitionContext: UIViewControllerContextTransitioning) {
        let fromViewController = transitionContext.viewController(forKey: .from)!
        let toViewController = transitionContext.viewController(forKey: .to)!
        transitionContext.containerView.insertSubview(toViewController.view, belowSubview: fromViewController.view)

        let center = fromViewController.view.center.x
        let right = fromViewController.view.center.x + fromViewController.view.frame.width
        let left = fromViewController.view.frame.width / 4;

        toViewController.view.center.x = CGFloat(left);

        UIView.animate(withDuration: duration, animations: {
            toViewController.view.center.x = center
            fromViewController.view.center.x = right
        }, completion: { _ in
            transitionContext.completeTransition(true)
        })
    }
}
