//
// Created by liumeng on 2021/4/16.
//

import Foundation
import UIKit

class PushAnimator: NSObject, UIViewControllerAnimatedTransitioning {
    let duration = 0.3

    func transitionDuration(using transitionContext: UIViewControllerContextTransitioning?) -> TimeInterval {
        return duration
    }

    func animateTransition(using transitionContext: UIViewControllerContextTransitioning) {
        let fromViewController = transitionContext.viewController(forKey: .from)!
        let toViewController = transitionContext.viewController(forKey: .to)!
        transitionContext.containerView.addSubview(toViewController.view)

        let center = toViewController.view.center.x
        let right = toViewController.view.center.x + toViewController.view.frame.width
        let left = toViewController.view.frame.width / 4;

        toViewController.view.center.x = right;

        UIView.animate(withDuration: duration, animations: {
            toViewController.view.center.x = center
            fromViewController.view.center.x = CGFloat(left)
        }, completion: { _ in
            transitionContext.completeTransition(true)
        })
    }
}
