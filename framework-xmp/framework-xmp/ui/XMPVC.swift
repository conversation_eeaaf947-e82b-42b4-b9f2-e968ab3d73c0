//
//  ViewController.swift
//  TestSwift2
//
//  Created by liumeng on 2021/3/16.
//
//

import UIKit
import WebKit
import Network

public class XMPVC: UIViewController, WKScriptMessageHandler, VCResultDelegate, UIViewControllerTransitioningDelegate, WKNavigationDelegate, XMPWebLoadingViewDelegate {

    @objc open var webView: WKWebView!
    var keyBoardPoint: CGPoint!
    @objc open var url: String?
    @objc open var resultDelegate: VCResultDelegate?
    var resultData: NSDictionary?
    var backView: UIView?
    let CACHE_MODE_NO_CACHE = "acheMode=noCache" // 不写开头c是为了兼容xxCacheMode=noCache这种情况
    let CACHE_MODE_BROWSER = "acheMode=browser" // 默认
    let CACHE_MODE_FORCE_CACHE = "acheMode=forceCache"
    let CACHE_MODE_OFFLINE_PACKAGE = "acheMode=offlinePackage"
    var loadingView: XMPWebLoadingView?
    var finishLoading = false
    open var hiddenBack = false

    // 网络监听相关
    private var networkMonitor: NWPathMonitor?
    private var networkQueue: DispatchQueue?
    private var isNetworkConnected = true // 记录当前网络状态

    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        // present动画相关配置
        transitioningDelegate = self
        modalPresentationStyle = .fullScreen
        // push相关
        hidesBottomBarWhenPushed = true
        // 解决iOS10 不全屏的问题（显示在状态栏下方）
        if #available(iOS 11.0, *) {} else {
            edgesForExtendedLayout = UIRectEdge(rawValue: 0) // 设置不延伸至导航栏，不用担心，viewWillAppear会隐藏导航栏
        }
    }

    public required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    public convenience init(urlStr: String) {
        self.init(nibName: nil, bundle: nil)
        url = urlStr
    }

    override public func viewDidLoad() {
        super.viewDidLoad()
        // 更新状态栏文字颜色
        setNeedsStatusBarAppearanceUpdate()
        // 设置iosttp和iosttps scheme
        let config = WKWebViewConfiguration()
        XMPCacheManager.shared.setWKURLSchemeHandler(config: config)
        XMPWKManager.shared.setWKWebViewUniqueProcessPool(config: config)
        // 传入webview
        webView = WKWebView(frame: UIScreen.main.bounds, configuration: config)
        view.addSubview(webView)
        // 设置UA
        setUA(webView: webView)
        // 设置WKNavigationDelegate
        webView.navigationDelegate = self
        // 忽略默认的安全距离
        if #available(iOS 11.0, *) {
            webView.scrollView.contentInsetAdjustmentBehavior = .never
        }
        // 处理返回键
        if !hiddenBack {
            let imageView = UIImageView.init(frame: CGRect(x: 13, y: 12, width: 20, height: 20))
            imageView.image = getImage(named: "Image_back")
            imageView.isUserInteractionEnabled = true
            var marginTop = 12 + getStatusBarHeightPt()
            // 兼容KOO App，KOO App中使用xmp-build.js 1.0版本，其中statusBarHeight默认写死成36dp（px）了
            if let koo = Bundle.main.bundleIdentifier?.contains("koo"), koo {
                marginTop = 36 + 12
            }
            
            backView = UIView.init(frame: CGRect(x: 0, y: marginTop - 12, width: 46, height: 44))
            backView!.addSubview(imageView)
            webView.addSubview(backView!)
            let tapGestureRecognizer = UITapGestureRecognizer(target: self, action: #selector(backViewTapped(tapGestureRecognizer:)))
            backView!.addGestureRecognizer(tapGestureRecognizer)
        }
        if let urlStr = url,
           let urlObj = URL(string: urlStr) {
            // 添加jsbridge
            webView.configuration.userContentController.add(WeakScriptMessageDelegate(self), name: "xmp")
            webView.configuration.userContentController.add(WeakScriptMessageDelegate(self), name: "koosdk") // 兼容KOOSDK
            // 加载页面
            if urlStr.contains(CACHE_MODE_NO_CACHE) {
                var request = URLRequest(url: urlObj)
                request.cachePolicy = .reloadIgnoringLocalAndRemoteCacheData
                webView.load(request)
            } else if urlStr.contains(CACHE_MODE_FORCE_CACHE) || urlStr.contains(CACHE_MODE_OFFLINE_PACKAGE) {
                XMPCacheManager.shared.requestReturnCacheDataElseLoad(url: urlStr, webView: webView)
            } else {
                webView.load(URLRequest(url: urlObj))
            }
        }
        
        loadingView = XMPWebLoadingView.init(frame: CGRect.init(x: 0, y: getStatusBarHeightPt(), width: self.view.frame.size.width, height: 6))
        loadingView!.delegate = self
        self.view.addSubview(loadingView!)
        loadingView!.startLoading()

        // 设置网络监听
        setupNetworkMonitoring()
    }

    override public func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        webView.frame = view.bounds
    }

    private func getImage(named: String) -> UIImage? {
        var image = UIImage(named: named, in: Bundle(for: type(of: self)), compatibleWith: nil)
        if image == nil { // cocoapods源码打包时，会将图片放在 framework-xmp.bundle 下
            let path = Bundle(for: XMPVC.self).resourcePath?.appending("/framework-xmp.bundle")
            let resourceBundle = Bundle(path: path ?? "")
            image = UIImage(named: named, in: resourceBundle, compatibleWith: nil)
        }
        return image
    }

    @objc func onShow() {
        if resultData != nil {
            let resultJson = XMPUtil.dict2json(dict: resultData)
            evaluateJavaScript(method: "onNativeCall", params: "{\"method\":\"onShow\",\"data\": " + resultJson + "}")
            evaluateJavaScript(method: "onNativeCall", params: "{\"method\":\"activedPage\",\"data\": " + resultJson + "}") // 兼容KOOSDK
            resultData = nil
        } else {
            evaluateJavaScript(method: "onNativeCall", params: "{\"method\":\"onShow\",\"data\": {}}")
            evaluateJavaScript(method: "onNativeCall", params: "{\"method\":\"activedPage\",\"data\": {}}") // 兼容KOOSDK
        }
    }

    @objc func onHide() {
        evaluateJavaScript(method: "onNativeCall", params: "{\"method\":\"onHide\",\"data\": {}}")
        evaluateJavaScript(method: "onNativeCall", params: "{\"method\":\"leavePage\",\"data\": {}}") // 兼容KOOSDK
    }

    @objc func onUserDidTakeScreenshot() {
        evaluateJavaScript(method: "onNativeCall", params: "{\"method\":\"onScreenshot\",\"data\": {}}")
    }

    // MARK: - 网络监听

    private func setupNetworkMonitoring() {
        // 创建网络监听器
        networkMonitor = NWPathMonitor()
        networkQueue = DispatchQueue(label: "NetworkMonitor")

        networkMonitor?.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.handleNetworkStatusChange(path: path)
            }
        }

        // 开始监听
        networkMonitor?.start(queue: networkQueue!)
    }

    private func handleNetworkStatusChange(path: NWPath) {
        let isConnected = path.status == .satisfied

        // 检查是否从断开状态重新连接
        if !isNetworkConnected && isConnected {
            // 网络从断开状态重新连接
            onNetworkReconnected()
        }

        // 更新网络状态
        isNetworkConnected = isConnected
    }

    private func onNetworkReconnected() {
        // 获取当前时间戳
        let timestamp = Int64((Date().timeIntervalSince1970 * 1000.0).rounded())

        // 构建网络重连数据
        let networkData = [
            "timestamp": timestamp,
            "type": "reconnected"
        ] as [String : Any]

        // 转换为JSON字符串
        if let jsonData = try? JSONSerialization.data(withJSONObject: networkData, options: []),
           let jsonString = String(data: jsonData, encoding: .utf8) {
            evaluateJavaScript(method: "onNativeCall", params: "{\"method\":\"onNetworkReconnected\",\"data\": \(jsonString)}")
        } else {
            // 如果JSON序列化失败，发送简单的通知
            evaluateJavaScript(method: "onNativeCall", params: "{\"method\":\"onNetworkReconnected\",\"data\": {}}")
        }
    }

    private func stopNetworkMonitoring() {
        networkMonitor?.cancel()
        networkMonitor = nil
        networkQueue = nil
    }

    override public func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.setNavigationBarHidden(true, animated: false)
        XMPVCManager.shared.setTopXMPVC(topXMPVC: self)
        onShow()
        NotificationCenter.default.addObserver(self, selector: #selector(onShow), name: UIApplication.willEnterForegroundNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(onHide), name: UIApplication.didEnterBackgroundNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(onUserDidTakeScreenshot), name: UIApplication.userDidTakeScreenshotNotification, object: nil)
        // 解决iOS12 - 使用WKWebView出现input键盘将页面上顶不下移
        if #available(iOS 13.0, *) {} else {
            NotificationCenter.default.addObserver(self, selector: #selector(keyboardShow(note:)), name: UIResponder.keyboardWillShowNotification, object: nil)
            NotificationCenter.default.addObserver(self, selector: #selector(keyboardHidden(note:)), name: UIResponder.keyboardWillHideNotification, object: nil)
        }
    }

    override public func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        NotificationCenter.default.removeObserver(self)
        onHide()
    }

    func getStatusBarHeightPt() -> CGFloat {
        var statusBarHeightPt: CGFloat?
        if #available(iOS 13.0, *) {
            statusBarHeightPt = UIApplication.shared.windows.first?.windowScene?.statusBarManager?.statusBarFrame.height
        } else {
            statusBarHeightPt = UIApplication.shared.statusBarFrame.height
        }
        if statusBarHeightPt == nil {
            statusBarHeightPt = 36
        }
        return statusBarHeightPt!
    }

    func setUA(webView: WKWebView) {
        var debuggale = false
        #if DEBUG
            debuggale = true
        #else
            debuggale = false
        #endif
        if let currentMode = UIScreen.main.currentMode,
           let bundleIdentifier = Bundle.main.bundleIdentifier,
           let ua = webView.value(forKey: "userAgent") as? String,
           let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String {
            let width: String = currentMode.size.width.description
            let height: String = currentMode.size.height.description
           
            let statusBarHeight = currentMode.size.width / UIScreen.main.bounds.width * getStatusBarHeightPt()

            webView.customUserAgent = ua
            webView.customUserAgent?.append(" XMP/1.0 (")
            webView.customUserAgent?.append(bundleIdentifier)
            webView.customUserAgent?.append(":")
            webView.customUserAgent?.append(version)

            webView.customUserAgent?.append("; appDebuggable:")
            webView.customUserAgent?.append(String(debuggale))

            webView.customUserAgent?.append("; statusBarHeight:")
            webView.customUserAgent?.append(statusBarHeight.description)

            webView.customUserAgent?.append("; width:")
            webView.customUserAgent?.append(width)

            webView.customUserAgent?.append("; height:")
            webView.customUserAgent?.append(height)

            webView.customUserAgent?.append("; timestamp:")
            let millisecond = Int64((Date().timeIntervalSince1970 * 1000.0).rounded())
            let timeStamp = "\(millisecond)"

            webView.customUserAgent?.append(timeStamp)
            webView.customUserAgent?.append(")")
        }
    }

    public func onVCResult(data: NSDictionary) {
        resultData = data
    }

    @objc func backViewTapped(tapGestureRecognizer: UITapGestureRecognizer) {
        evaluateJavaScript(method: "onNativeCall", params: "{\"method\":\"onBackPress\",\"data\":{}}") { [self] response, _ in
            if let ret = response as? String, ret == "true" {} else {
                // 兼容KOOSDK
                evaluateJavaScript(method: "onNativeCall", params: "{\"method\":\"interceptBackPress\",\"data\":{}}") { response, _ in
                    if let ret = response as? String, ret == "true" {} else {
                        // 判断webview是否可以goback
                        if self.webView.canGoBack {
                            self.webView.goBack()
                        } else {
                            // 关闭页面
                            if self.navigationController != nil {
                                self.navigationController?.popViewController(animated: true)
                            } else {
                                self.dismiss(animated: true, completion: nil)
                            }
                        }
                    }
                }
            }
        }
    }

    override public var preferredStatusBarStyle: UIStatusBarStyle {
        if #available(iOS 13.0, *) {
            return .darkContent
        }
        return .default
    }

    // MARK: - JavaScript

    public func userContentController(_ userContentController: WKUserContentController, didReceive message: WKScriptMessage) {
        if message.name == "xmp" {
            PluginManager.shared.dispatch(message: message, viewController: self)
        }
    }

    func evaluateJavaScript(method: String, params: String?, completionHandler: ((Any?, Error?) -> Void)? = nil) {
        var tmpJs: String
        if params != nil {
            var p: String
            if params!.contains("'") {
                // 下面方式更通用，但由于历史原因，业务没有时间回归测试 故仅在有'时采用下面方法
                tmpJs = method + "(\"%@\")"
                p = params!.replacingOccurrences(of: "\\", with: "\\\\")
                    .replacingOccurrences(of: "\"", with: "\\\"")
            } else {
                tmpJs = method + "('%@')"
                p = params!.replacingOccurrences(of: "\\", with: "\\\\")
            }
            tmpJs = String(format: tmpJs, p)
        } else {
            tmpJs = method + "()"
        }
        // print(tmpJs)
        webView.evaluateJavaScript(tmpJs, completionHandler: completionHandler)
    }

    // MARK: - animation

    public func animationController(forPresented presented: UIViewController,
                                    presenting: UIViewController,
                                    source: UIViewController) -> UIViewControllerAnimatedTransitioning? {
        return PushAnimator()
    }

    public func animationController(forDismissed dismissed: UIViewController) -> UIViewControllerAnimatedTransitioning? {
        return PopAnimator()
    }

    // MARK: - 键盘监听

    // 键盘弹出监听
    @objc func keyboardShow(note: Notification) {
        let point = webView.scrollView.contentOffset
        keyBoardPoint = point
    }

    // 键盘隐藏监听
    @objc func keyboardHidden(note: Notification) {
        if let _ = keyBoardPoint {
            webView.scrollView.contentOffset = keyBoardPoint
        }
    }
    
    
    func showLoadingViewFinish() -> Bool {
        return self.finishLoading
    }

    public func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        XMPUtil.reportWebViewError(self, webView, error as NSError, false)
        self.finishLoading = true
    }

    public func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        XMPUtil.reportWebViewError(self, webView, error as NSError, true)
        self.finishLoading = true
    }
    
    public func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        self.finishLoading = true
    }
    
    public func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
        // 拦截电话和邮件
        if let url = navigationAction.request.url, (url.scheme == "tel" || url.scheme == "mailto"), UIApplication.shared.canOpenURL(url)  {
            if #available(iOS 10.0, *) {
                UIApplication.shared.open(url)
            } else {
                UIApplication.shared.openURL(url)
            }
            decisionHandler(.cancel)
            return
        }
        decisionHandler(.allow)
    }
}

class WeakScriptMessageDelegate: NSObject, WKScriptMessageHandler {
    private weak var scriptDelegate: WKScriptMessageHandler?

    init(_ scriptDelegate: WKScriptMessageHandler) {
        super.init()
        self.scriptDelegate = scriptDelegate
    }

    func userContentController(_ userContentController: WKUserContentController, didReceive message: WKScriptMessage) {
        scriptDelegate?.userContentController(userContentController, didReceive: message)
    }
}
