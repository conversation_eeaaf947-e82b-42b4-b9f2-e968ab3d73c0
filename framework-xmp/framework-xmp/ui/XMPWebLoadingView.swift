//
//  XMPWebLoadingView.swift
//  framework-xmp
//
//  Created by ya<PERSON><PERSON><PERSON> on 2024/9/24.
//

import UIKit

protocol XMPWebLoadingViewDelegate: NSObject {
    func showLoadingViewFinish() -> Bool
}


class XMPWebLoadingView: UIProgressView {
    var timer: Timer?
    weak var delegate: XMPWebLoadingViewDelegate?
    deinit{
        self.stopLoading()
    }
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.trackTintColor = UIColor.clear
        self.progressViewStyle = .default
        self.progressTintColor = xmpColorWithHex(hexStr: "0x00903B", alpha: 1)
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
    }
    
    func startLoading() {
        self.isHidden = false
        self.progress = 0
        self.clearTimer()
        timer = Timer.scheduledTimer(withTimeInterval: 0.02, repeats: true, block: {[weak self] timer in
            self?.updataLoadProgress()
        })
        if let safeTimer = timer {
            RunLoop.main.add(safeTimer, forMode: .common)
            safeTimer.fire()
        }
       
    }
    
    func clearTimer()  {
        if let safeTimer = timer {
            safeTimer.invalidate()
            timer = nil
        }
    }
    
    func stopLoading()  {
        self.isHidden = true
        self.clearTimer()
    }

    func updataLoadProgress() {
        if self.progress >= 1 {
            self.isHidden = true
            self.clearTimer()
        }
        
        var finished = false
        
        if let safeDelegate = self.delegate {
            finished = safeDelegate.showLoadingViewFinish()
        }
        
        var step = 0.06
        if !finished {
            if (self.progress < 0.6) {
                step = 0.004;
            } else if (self.progress < 0.85) {
                step = 0.001;
            } else if (self.progress < 0.95) {
                step = 0.0001;
            }
        }
        self.progress += Float(step)
    }
    
    func xmpColorWithHex(hexStr:String, alpha:Float) -> UIColor{
        var cStr: NSString = hexStr as NSString;
            
            if(cStr.length < 6){
                return UIColor.clear;
            }
            
            if(cStr.hasPrefix("0x")) {
                cStr = cStr.substring(from: 2) as NSString
            }
            
            if(cStr.hasPrefix("#")){
                cStr = cStr.substring(from: 1) as NSString
            }
            
        
            if(cStr.length != 6){
                return UIColor.clear;
            }
            
            let rStr = (cStr as NSString).substring(to: 2)
            let gStr = ((cStr as NSString).substring(from: 2) as NSString).substring(to: 2)
            let bStr = ((cStr as NSString).substring(from: 4) as NSString).substring(to: 2)
            
            var r : UInt32 = 0x0
            var g : UInt32 = 0x0
            var b : UInt32 = 0x0
            
            Scanner.init(string: rStr).scanHexInt32(&r);
            Scanner.init(string: gStr).scanHexInt32(&g);
            Scanner.init(string: bStr).scanHexInt32(&b);
            
            return UIColor.init(red: CGFloat(r)/255.0, green: CGFloat(g)/255.0, blue: CGFloat(b)/255.0, alpha: CGFloat(alpha));
            
        }
}
