//
//  XMPVCManager.swift
//  xmplib
//
//  Created by lium<PERSON> on 2023/3/21.
//

import Foundation
import UIKit
public class XMPVCManager: NSObject {
    public static let shared = XMPVCManager()

    private weak var topXMPVC: XMPVC?

    override private init() {
        super.init()
    }

    func setTopXMPVC(topXMPVC: XMPVC) {
        self.topXMPVC = topXMPVC
    }

    func goBackToContainer(patternOrNum: String, data: NSDictionary) {
        let num = Int(patternOrNum)
        if num != nil {
            // num 可以取值 -1、-2、、、
            findTarget(current: topXMPVC, patternOrNum: num! * -1, data: data, currentIndex: 1)
        } else {
            findTarget(current: topXMPVC, patternOrNum: patternOrNum, data: data)
        }
    }

    func notifyH5(event: String, data: NSDictionary) {
        let dataJsonString = XMPUtil.dict2json(dict: data)
        notifyTarget(current: topXMPVC, event: event, data: dataJsonString)
    }

    func findTarget(current: XMPVC?, patternOrNum: String, data: NSDictionary) {
        if let target = current?.resultDelegate as? XMPVC,
           let url = target.url,
           let re = try? NSRegularExpression(pattern: patternOrNum) {
            if target.resultDelegate == nil {
                target.resultData = data
                closeXMPVC(xmpVC: target)
                return
            }
            let matches = re.matches(in: url, range: NSMakeRange(0, url.count))
            if matches.count <= 0 {
                findTarget(current: target, patternOrNum: patternOrNum, data: data)
            } else {
                target.resultData = data
                closeXMPVC(xmpVC: target)
            }
        }
    }

    func findFirstXmpVC(current: XMPVC?) -> XMPVC? {
        if current?.resultDelegate == nil { return current}
        return findFirstXmpVC(current: current?.resultDelegate as? XMPVC)
    }
    
    
    func findTarget(current: XMPVC?, patternOrNum: Int, data: NSDictionary, currentIndex: Int) {
        if let target = current?.resultDelegate as? XMPVC {
            if target.resultDelegate == nil {
                target.resultData = data
                closeXMPVC(xmpVC: target)
                return
            }
            if patternOrNum != currentIndex {
                findTarget(current: target, patternOrNum: patternOrNum, data: data, currentIndex: currentIndex + 1)
            } else {
                target.resultData = data
                closeXMPVC(xmpVC: target)
            }
        }
    }

    func closeXMPVC(xmpVC: XMPVC?) {
        if xmpVC?.navigationController != nil {
            if let safeParentVC = xmpVC?.parent as? UITabBarController {
                safePopViewController(safeParentVC)
            } else {
                safePopViewController(xmpVC!)
            }
        } else {
            xmpVC?.dismiss(animated: true, completion: nil)
        }
    }

    func notifyTarget(current: XMPVC?, event: String, data: String) {
        current?.evaluateJavaScript(method: "onNativeCall",
                                    params: "{\"method\":\"onEvent" + event + "\",\"data\": " + data + "}")
        if let next = current?.resultDelegate as? XMPVC {
            notifyTarget(current: next, event: event, data: data)
        }
    }
    
    private func safePopViewController(_ toVC: UIViewController) {
        if toVC.navigationController?.viewControllers.contains(toVC) == true {
            toVC.navigationController?.popToViewController(toVC, animated: true)
        }
    }
    
}
