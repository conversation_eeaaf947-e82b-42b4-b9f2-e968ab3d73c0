//
// Created by l<PERSON><PERSON> on 2021/4/13.
//

import Foundation
import WebKit

class XMPUtil {

    static func json2dict(text: String?) -> [String: Any] {
        if let data = text?.data(using: .utf8) {
            do {
                return try JSONSerialization.jsonObject(with: data, options: []) as! [String: Any]
            } catch {
                print(error.localizedDescription)
            }
        }
        return [:]
    }

    static func dict2json(dict: NSDictionary?) -> String {
        if let notNullDict = dict {
            do {
                let data = try JSONSerialization.data(withJSONObject: notNullDict, options: [])
                let nsString = NSString(data: data, encoding: String.Encoding.utf8.rawValue)
                return nsString! as String;
            } catch {
                print(error.localizedDescription)
                return "{}"
            }
        } else {
            return "{}"
        }
    }
    
    static func reportWebViewError(_ viewController: XMPVC,_ webView: WKWebView, _ error: NSError, _ didFailProvisionalNavigation: Bool) {
        var dic = Dictionary<String, Any>()
        dic["event"] = "webview_error"
        dic["error"] = error
        dic["didFailProvisionalNavigation"] = didFailProvisionalNavigation
        let message: PluginManagerMessage = PluginManagerMessage.init(method: "report", data: dic)
        PluginManager.shared.dispatchMessage(message, viewController)
    }
    
    
}
