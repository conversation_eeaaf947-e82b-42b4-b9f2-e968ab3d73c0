//
//  XMPWKManager.swift
//  framework-xmp
//
//  Created by l<PERSON><PERSON> on 2024/6/21.
//

import Foundation
import WebKit
public class XMPWKManager: NSObject {
    public static let shared = XMPWKManager()
    let uniqueProcessPool = WKProcessPool()

    override private init() {
        super.init()
    }

    func setWKWebViewUniqueProcessPool(config: WKWebViewConfiguration) {
        config.processPool = uniqueProcessPool
    }
    
}
