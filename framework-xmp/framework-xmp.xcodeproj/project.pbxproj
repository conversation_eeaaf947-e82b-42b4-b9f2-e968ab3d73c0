// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 50;
	objects = {

/* Begin PBXBuildFile section */
		8714D6292A67F92500E40A64 /* XMPCacheManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8714D6272A67F92500E40A64 /* XMPCacheManager.swift */; };
		8714D62A2A67F92500E40A64 /* XMPUpdateManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8714D6282A67F92500E40A64 /* XMPUpdateManager.swift */; };
		8714D62F2A67F96700E40A64 /* NotSupportedPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8714D62B2A67F96700E40A64 /* NotSupportedPlugin.swift */; };
		8714D6302A67F96700E40A64 /* NotifyH5Plugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8714D62C2A67F96700E40A64 /* NotifyH5Plugin.swift */; };
		8714D6312A67F96700E40A64 /* GoBackToContainerPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8714D62D2A67F96700E40A64 /* GoBackToContainerPlugin.swift */; };
		8714D6322A67F96700E40A64 /* LogPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8714D62E2A67F96700E40A64 /* LogPlugin.swift */; };
		8714D6352A67F99400E40A64 /* XMPRegex.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8714D6332A67F99400E40A64 /* XMPRegex.swift */; };
		8714D6362A67F99400E40A64 /* XMPVCManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8714D6342A67F99400E40A64 /* XMPVCManager.swift */; };
		8714D6382A67FA2500E40A64 /* framework-xmp.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 8714D6372A67FA2500E40A64 /* framework-xmp.bundle */; };
		872C02BE2C2527B50000A127 /* XMPWKManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 872C02BD2C2527B50000A127 /* XMPWKManager.swift */; };
		875A2D4D262E78E100CD15FF /* XMPUtil.swift in Sources */ = {isa = PBXBuildFile; fileRef = 875A2D3D262E768700CD15FF /* XMPUtil.swift */; };
		87748CC22AE279D9002E03D0 /* xmplib.sh in Resources */ = {isa = PBXBuildFile; fileRef = 87748CC12AE279D9002E03D0 /* xmplib.sh */; };
		87748CC42AE27A58002E03D0 /* xmplib.podspec in Resources */ = {isa = PBXBuildFile; fileRef = 87748CC32AE27A58002E03D0 /* xmplib.podspec */; };
		AF1AB1D737BACEA0946F4994 /* CloseContainerPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF1AB78674E4D5A69CEB23C2 /* CloseContainerPlugin.swift */; };
		AF1AB28E85E95ECC09719C2D /* DismissBackButtonPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF1AB12DAB741ACB9D79E819 /* DismissBackButtonPlugin.swift */; };
		AF1AB3B17B302B2EB0A354C1 /* Plugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF1AB2B14DE597C217AC6801 /* Plugin.swift */; };
		AF1AB3D7922566D477575CE2 /* PushAnimator.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF1AB598460C43FD10389470 /* PushAnimator.swift */; };
		AF1AB905106A3CC774475C2A /* XMPVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF1AB70864EC37CD7487D111 /* XMPVC.swift */; };
		AF1ABAC6591E9F104011B4AB /* PluginManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF1ABB54E4837F6874CD2860 /* PluginManager.swift */; };
		AF1ABBDB7F14D36BB1413C31 /* VCResultDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF1ABEC11B41FF55487C0928 /* VCResultDelegate.swift */; };
		AF1ABD012DB1FDDAC5E11076 /* PopAnimator.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF1AB84EA70914374D0648AF /* PopAnimator.swift */; };
		AF1ABD83BD30FEC26B287BB8 /* OpenContainerPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = AF1AB77DD0F5BE1740E5D3E2 /* OpenContainerPlugin.swift */; };
		B9BCAB382CA2A40800EBDBAD /* XMPWebLoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B9BCAB372CA2A40800EBDBAD /* XMPWebLoadingView.swift */; };
		B9FA9FDD2C33D912007E888B /* NotifyNativePlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = B9FA9FDC2C33D912007E888B /* NotifyNativePlugin.swift */; };
		B9FA9FDF2C33D9FA007E888B /* CloseAllPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = B9FA9FDE2C33D9FA007E888B /* CloseAllPlugin.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		8714D6272A67F92500E40A64 /* XMPCacheManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMPCacheManager.swift; sourceTree = "<group>"; };
		8714D6282A67F92500E40A64 /* XMPUpdateManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMPUpdateManager.swift; sourceTree = "<group>"; };
		8714D62B2A67F96700E40A64 /* NotSupportedPlugin.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NotSupportedPlugin.swift; sourceTree = "<group>"; };
		8714D62C2A67F96700E40A64 /* NotifyH5Plugin.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NotifyH5Plugin.swift; sourceTree = "<group>"; };
		8714D62D2A67F96700E40A64 /* GoBackToContainerPlugin.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GoBackToContainerPlugin.swift; sourceTree = "<group>"; };
		8714D62E2A67F96700E40A64 /* LogPlugin.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LogPlugin.swift; sourceTree = "<group>"; };
		8714D6332A67F99400E40A64 /* XMPRegex.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMPRegex.swift; sourceTree = "<group>"; };
		8714D6342A67F99400E40A64 /* XMPVCManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMPVCManager.swift; sourceTree = "<group>"; };
		8714D6372A67FA2500E40A64 /* framework-xmp.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = "framework-xmp.bundle"; sourceTree = "<group>"; };
		872C02BD2C2527B50000A127 /* XMPWKManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMPWKManager.swift; sourceTree = "<group>"; };
		875A2D05262E701F00CD15FF /* framework_xmp.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = framework_xmp.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		875A2D09262E701F00CD15FF /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		875A2D3D262E768700CD15FF /* XMPUtil.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMPUtil.swift; sourceTree = "<group>"; };
		87748CAF2AE26B82002E03D0 /* framework-xmp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "framework-xmp.h"; sourceTree = "<group>"; };
		87748CC12AE279D9002E03D0 /* xmplib.sh */ = {isa = PBXFileReference; lastKnownFileType = text.script.sh; path = xmplib.sh; sourceTree = "<group>"; };
		87748CC32AE27A58002E03D0 /* xmplib.podspec */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = xmplib.podspec; sourceTree = "<group>"; };
		AF1AB12DAB741ACB9D79E819 /* DismissBackButtonPlugin.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DismissBackButtonPlugin.swift; sourceTree = "<group>"; };
		AF1AB2B14DE597C217AC6801 /* Plugin.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Plugin.swift; sourceTree = "<group>"; };
		AF1AB598460C43FD10389470 /* PushAnimator.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PushAnimator.swift; sourceTree = "<group>"; };
		AF1AB70864EC37CD7487D111 /* XMPVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMPVC.swift; sourceTree = "<group>"; };
		AF1AB77DD0F5BE1740E5D3E2 /* OpenContainerPlugin.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OpenContainerPlugin.swift; sourceTree = "<group>"; };
		AF1AB78674E4D5A69CEB23C2 /* CloseContainerPlugin.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CloseContainerPlugin.swift; sourceTree = "<group>"; };
		AF1AB84EA70914374D0648AF /* PopAnimator.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PopAnimator.swift; sourceTree = "<group>"; };
		AF1ABB54E4837F6874CD2860 /* PluginManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PluginManager.swift; sourceTree = "<group>"; };
		AF1ABEC11B41FF55487C0928 /* VCResultDelegate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = VCResultDelegate.swift; sourceTree = "<group>"; };
		B9BCAB372CA2A40800EBDBAD /* XMPWebLoadingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMPWebLoadingView.swift; sourceTree = "<group>"; };
		B9FA9FDC2C33D912007E888B /* NotifyNativePlugin.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotifyNativePlugin.swift; sourceTree = "<group>"; };
		B9FA9FDE2C33D9FA007E888B /* CloseAllPlugin.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CloseAllPlugin.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		875A2D02262E701F00CD15FF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		875A2CFB262E701F00CD15FF = {
			isa = PBXGroup;
			children = (
				875A2D07262E701F00CD15FF /* framework-xmp */,
				875A2D06262E701F00CD15FF /* Products */,
			);
			sourceTree = "<group>";
		};
		875A2D06262E701F00CD15FF /* Products */ = {
			isa = PBXGroup;
			children = (
				875A2D05262E701F00CD15FF /* framework_xmp.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		875A2D07262E701F00CD15FF /* framework-xmp */ = {
			isa = PBXGroup;
			children = (
				AF1AB84580E0032575486ABE /* bridge */,
				AF1AB326223490E554355CB2 /* cache */,
				AF1AB1DCDB700E1069B64B84 /* ui */,
				875A2D3E262E768700CD15FF /* utils */,
				8714D6372A67FA2500E40A64 /* framework-xmp.bundle */,
				87748CAF2AE26B82002E03D0 /* framework-xmp.h */,
				87748CC12AE279D9002E03D0 /* xmplib.sh */,
				87748CC32AE27A58002E03D0 /* xmplib.podspec */,
				875A2D09262E701F00CD15FF /* Info.plist */,
			);
			path = "framework-xmp";
			sourceTree = "<group>";
		};
		875A2D3E262E768700CD15FF /* utils */ = {
			isa = PBXGroup;
			children = (
				8714D6332A67F99400E40A64 /* XMPRegex.swift */,
				8714D6342A67F99400E40A64 /* XMPVCManager.swift */,
				872C02BD2C2527B50000A127 /* XMPWKManager.swift */,
				875A2D3D262E768700CD15FF /* XMPUtil.swift */,
			);
			path = utils;
			sourceTree = "<group>";
		};
		AF1AB1DCDB700E1069B64B84 /* ui */ = {
			isa = PBXGroup;
			children = (
				AF1AB70864EC37CD7487D111 /* XMPVC.swift */,
				AF1ABEC11B41FF55487C0928 /* VCResultDelegate.swift */,
				AF1AB598460C43FD10389470 /* PushAnimator.swift */,
				AF1AB84EA70914374D0648AF /* PopAnimator.swift */,
				B9BCAB372CA2A40800EBDBAD /* XMPWebLoadingView.swift */,
			);
			path = ui;
			sourceTree = "<group>";
		};
		AF1AB326223490E554355CB2 /* cache */ = {
			isa = PBXGroup;
			children = (
				8714D6272A67F92500E40A64 /* XMPCacheManager.swift */,
				8714D6282A67F92500E40A64 /* XMPUpdateManager.swift */,
			);
			path = cache;
			sourceTree = "<group>";
		};
		AF1AB84580E0032575486ABE /* bridge */ = {
			isa = PBXGroup;
			children = (
				8714D62D2A67F96700E40A64 /* GoBackToContainerPlugin.swift */,
				8714D62E2A67F96700E40A64 /* LogPlugin.swift */,
				8714D62C2A67F96700E40A64 /* NotifyH5Plugin.swift */,
				8714D62B2A67F96700E40A64 /* NotSupportedPlugin.swift */,
				AF1ABB54E4837F6874CD2860 /* PluginManager.swift */,
				AF1AB2B14DE597C217AC6801 /* Plugin.swift */,
				AF1AB77DD0F5BE1740E5D3E2 /* OpenContainerPlugin.swift */,
				AF1AB78674E4D5A69CEB23C2 /* CloseContainerPlugin.swift */,
				AF1AB12DAB741ACB9D79E819 /* DismissBackButtonPlugin.swift */,
				B9FA9FDC2C33D912007E888B /* NotifyNativePlugin.swift */,
				B9FA9FDE2C33D9FA007E888B /* CloseAllPlugin.swift */,
			);
			path = bridge;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		875A2D00262E701F00CD15FF /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		875A2D04262E701F00CD15FF /* framework-xmp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 875A2D0D262E701F00CD15FF /* Build configuration list for PBXNativeTarget "framework-xmp" */;
			buildPhases = (
				875A2D00262E701F00CD15FF /* Headers */,
				875A2D01262E701F00CD15FF /* Sources */,
				875A2D02262E701F00CD15FF /* Frameworks */,
				875A2D03262E701F00CD15FF /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "framework-xmp";
			productName = xmplib;
			productReference = 875A2D05262E701F00CD15FF /* framework_xmp.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		875A2CFC262E701F00CD15FF /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1240;
				TargetAttributes = {
					875A2D04262E701F00CD15FF = {
						CreatedOnToolsVersion = 12.4;
						LastSwiftMigration = 1240;
					};
				};
			};
			buildConfigurationList = 875A2CFF262E701F00CD15FF /* Build configuration list for PBXProject "framework-xmp" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 875A2CFB262E701F00CD15FF;
			productRefGroup = 875A2D06262E701F00CD15FF /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				875A2D04262E701F00CD15FF /* framework-xmp */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		875A2D03262E701F00CD15FF /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				87748CC42AE27A58002E03D0 /* xmplib.podspec in Resources */,
				87748CC22AE279D9002E03D0 /* xmplib.sh in Resources */,
				8714D6382A67FA2500E40A64 /* framework-xmp.bundle in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		875A2D01262E701F00CD15FF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8714D6362A67F99400E40A64 /* XMPVCManager.swift in Sources */,
				8714D6292A67F92500E40A64 /* XMPCacheManager.swift in Sources */,
				875A2D4D262E78E100CD15FF /* XMPUtil.swift in Sources */,
				8714D62A2A67F92500E40A64 /* XMPUpdateManager.swift in Sources */,
				AF1ABAC6591E9F104011B4AB /* PluginManager.swift in Sources */,
				8714D6312A67F96700E40A64 /* GoBackToContainerPlugin.swift in Sources */,
				AF1AB3B17B302B2EB0A354C1 /* Plugin.swift in Sources */,
				8714D62F2A67F96700E40A64 /* NotSupportedPlugin.swift in Sources */,
				B9BCAB382CA2A40800EBDBAD /* XMPWebLoadingView.swift in Sources */,
				B9FA9FDF2C33D9FA007E888B /* CloseAllPlugin.swift in Sources */,
				AF1ABD83BD30FEC26B287BB8 /* OpenContainerPlugin.swift in Sources */,
				8714D6352A67F99400E40A64 /* XMPRegex.swift in Sources */,
				B9FA9FDD2C33D912007E888B /* NotifyNativePlugin.swift in Sources */,
				8714D6302A67F96700E40A64 /* NotifyH5Plugin.swift in Sources */,
				AF1AB1D737BACEA0946F4994 /* CloseContainerPlugin.swift in Sources */,
				AF1AB28E85E95ECC09719C2D /* DismissBackButtonPlugin.swift in Sources */,
				8714D6322A67F96700E40A64 /* LogPlugin.swift in Sources */,
				AF1AB905106A3CC774475C2A /* XMPVC.swift in Sources */,
				AF1ABBDB7F14D36BB1413C31 /* VCResultDelegate.swift in Sources */,
				AF1AB3D7922566D477575CE2 /* PushAnimator.swift in Sources */,
				872C02BE2C2527B50000A127 /* XMPWKManager.swift in Sources */,
				AF1ABD012DB1FDDAC5E11076 /* PopAnimator.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		875A2D0B262E701F00CD15FF /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		875A2D0C262E701F00CD15FF /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		875A2D0E262E701F00CD15FF /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = SU25TSGG47;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = "framework-xmp/Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.xinye.framework-xmp";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SUPPORTS_MACCATALYST = NO;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		875A2D0F262E701F00CD15FF /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = SU25TSGG47;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = "framework-xmp/Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.xinye.framework-xmp";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SUPPORTS_MACCATALYST = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		875A2CFF262E701F00CD15FF /* Build configuration list for PBXProject "framework-xmp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				875A2D0B262E701F00CD15FF /* Debug */,
				875A2D0C262E701F00CD15FF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		875A2D0D262E701F00CD15FF /* Build configuration list for PBXNativeTarget "framework-xmp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				875A2D0E262E701F00CD15FF /* Debug */,
				875A2D0F262E701F00CD15FF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 875A2CFC262E701F00CD15FF /* Project object */;
}
