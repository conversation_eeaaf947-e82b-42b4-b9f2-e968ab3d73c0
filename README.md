# iOS XMP 强缓存模式依赖 h5站点的cache-control设置成永久缓存

# 工具版本记录
```shell
pod --version
1.12.1
```

# 发布命令
```shell
1、在需要发布的commit上 打上版本tag  推到远程
2、修改 framework-xmp/framework-xmp/xmplib.podspec 中的 tag 推到远程
3、根目录执行 ./framework-xmp/framework-xmp/xmplib.sh
```

# 其他命令记录
```shell
xcodebuild -project xmplib.xcodeproj -scheme xmplib -configuration Release -sdk iphoneos
iphonesimulator

lipo -create /Users/<USER>/Library/Developer/Xcode/DerivedData/xmplib-fbcggvrqquhtrjayhhvkoutcwckw/Build/Products/Release-iphoneos/xmplib.framework/xmplib /Users/<USER>/Library/Developer/Xcode/DerivedData/xmplib-fbcggvrqquhtrjayhhvkoutcwckw/Build/Products/Release-iphonesimulator/xmplib.framework/xmplib -output /Users/<USER>/Library/Developer/Xcode/DerivedData/xmplib-fbcggvrqquhtrjayhhvkoutcwckw/Build/Products/Release-iphoneos/xmplib.framework/xmplib


lipo -info /Users/<USER>/Library/Developer/Xcode/DerivedData/xmplib-fbcggvrqquhtrjayhhvkoutcwckw/Build/Products/Release-iphoneos/xmplib.framework/xmplib

lipo -info /Users/<USER>/Library/Developer/Xcode/DerivedData/xmplib-fbcggvrqquhtrjayhhvkoutcwckw/Build/Products/Release-iphonesimulator/xmplib.framework/xmplib

lipo -remove arm64 /Users/<USER>/Library/Developer/Xcode/DerivedData/xmplib-fbcggvrqquhtrjayhhvkoutcwckw/Build/Products/Release-iphonesimulator/xmplib.framework/xmplib -output /Users/<USER>/Library/Developer/Xcode/DerivedData/xmplib-fbcggvrqquhtrjayhhvkoutcwckw/Build/Products/Release-iphonesimulator/xmplib.framework/xmplib


zip -r xmplib.zip LICENSE xmplib.framework

pod spec lint xmplib.podspec --verbose --use-libraries

pod repo <NAME_EMAIL>:wtlucky/WTSpecs.git
pod repo push XMP_Spec xmplib.podspec 
pod repo add XMP_Spec *********************:XMP/XMP_Spec.git
```



